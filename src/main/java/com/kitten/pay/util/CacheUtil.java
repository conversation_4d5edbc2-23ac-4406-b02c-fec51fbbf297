// package com.kitten.pay.util;
//
// import com.baomidou.mybatisplus.core.toolkit.StringPool;
// import lombok.extern.slf4j.Slf4j;
// import org.springframework.cache.Cache;
// import org.springframework.cache.CacheManager;
// import org.springframework.data.redis.core.Cursor;
// import org.springframework.data.redis.core.RedisTemplate;
// import org.springframework.data.redis.core.ScanOptions;
// import org.springframework.lang.Nullable;
// import org.springframework.util.StringUtils;
//
// import java.lang.reflect.Array;
// import java.nio.charset.StandardCharsets;
// import java.util.*;
// import java.util.concurrent.Callable;
// import java.util.stream.Collectors;
//
// /**
//  * 缓存工具类
//  *
//  * <AUTHOR>
//  */
// @Slf4j
// public class CacheUtil {
//
// 	private static CacheManager cacheManager;
// 	private static RedisTemplate<Object, Object> redisTemplate;
//
// 	/**
// 	 * redis配置类会在启动时初始化
// 	 */
// 	public static void init(CacheManager cacheManager, RedisTemplate<Object, Object> redisTemplate) {
// 		CacheUtil.cacheManager = cacheManager;
// 		CacheUtil.redisTemplate = redisTemplate;
// 	}
//
// 	/**
// 	 * 获取缓存工具
// 	 *
// 	 * @return CacheManager
// 	 */
// 	private static CacheManager getCacheManager() {
// 		return cacheManager;
// 	}
//
// 	/**
// 	 * 获取缓存对象
// 	 *
// 	 * @param cacheName  缓存名
// 	 * @return Cache
// 	 */
// 	public static Cache getCache(String cacheName) {
// 		return getCacheManager().getCache(cacheName);
// 	}
//
// 	/**
// 	 * 格式化缓存名（备用）
// 	 *
// 	 * @param key  缓存名
// 	 * @return String
// 	 */
// 	public static String formatKey(Object... key) {
// 		return Arrays.stream(key).filter(Objects::nonNull).map(Object::toString).collect(Collectors.joining(StringPool.COLON));
// 	}
//
//
// 	/**
// 	 * 获取缓存
// 	 *
// 	 * @param cacheName  缓存名
// 	 * @param keyPrefix  缓存键前缀
// 	 * @param key        缓存键值
// 	 * @return Object
// 	 */
// 	@Nullable
// 	public static Object get(String cacheName, String keyPrefix, Object key) {
// 		if (StringUtils.isEmpty(cacheName) || isEmpty(key)) {
// 			return null;
// 		}
// 		Cache.ValueWrapper valueWrapper = getCache(cacheName).get(formatKey(keyPrefix, key));
// 		if (isEmpty(valueWrapper)) {
// 			return null;
// 		}
// 		return valueWrapper.get();
// 	}
//
// 	/**
// 	 * 获取缓存
// 	 *
// 	 * @param cacheName  缓存名
// 	 * @param keyPrefix  缓存键前缀
// 	 * @param key        缓存键值
// 	 * @param type       类型
// 	 * @param <T>        泛型
// 	 * @return T
// 	 */
// 	@Nullable
// 	public static <T> T get(String cacheName, String keyPrefix, Object key, @Nullable Class<T> type) {
// 		if (StringUtils.isEmpty(cacheName) || isEmpty(key)) {
// 			return null;
// 		}
// 		return getCache(cacheName).get(formatKey(keyPrefix, key), type);
// 	}
//
//
// 	/**
// 	 * 获取缓存
// 	 *
// 	 * @param cacheName   缓存名
// 	 * @param keyPrefix   缓存键前缀
// 	 * @param key         缓存键值
// 	 * @param valueLoader 重载对象
// 	 * @param <T>         泛型
// 	 * @return T
// 	 */
// 	@Nullable
// 	public static <T> T get(String cacheName, String keyPrefix, Object key, Callable<T> valueLoader) {
// 		if (StringUtils.isEmpty(cacheName) || isEmpty(key)) {
// 			return null;
// 		}
// 		try {
// 			Cache.ValueWrapper valueWrapper = getCache(cacheName).get(formatKey(keyPrefix, key));
// 			Object value = null;
// 			if (valueWrapper == null) {
// 				T call = valueLoader.call();
// 				if (!isEmpty(call)) {
// 					getCache(cacheName).put(formatKey(keyPrefix, key), call);
// 					value = call;
// 				}
// 			} else {
// 				value = valueWrapper.get();
// 			}
// 			if (value == null) {
// 				return null;
// 			}
// 			return (T) value;
// 		} catch (Exception ex) {
// 			log.error("获取缓存失败", ex);
// 			return null;
// 		}
// 	}
//
// 	/**
// 	 * 获取缓存
// 	 *
// 	 * @param cacheName   缓存名
// 	 * @param key         缓存键值
// 	 * @param valueLoader 重载对象
// 	 * @param <T>         泛型
// 	 * @return T
// 	 */
// 	@Nullable
// 	public static <T> T get(String cacheName, Object key, Callable<T> valueLoader) {
// 		if (StringUtils.isEmpty(cacheName) || isEmpty(key)) {
// 			return null;
// 		}
// 		try {
// 			Cache.ValueWrapper valueWrapper = getCache(cacheName).get(key);
// 			Object value = null;
// 			if (valueWrapper == null) {
// 				T call = valueLoader.call();
// 				if (!isEmpty(call)) {
// 					getCache(cacheName).put(key, call);
// 					value = call;
// 				}
// 			} else {
// 				value = valueWrapper.get();
// 			}
// 			if (value == null) {
// 				return null;
// 			}
// 			return (T) value;
// 		} catch (Exception ex) {
// 			log.error("获取缓存失败", ex);
// 			return null;
// 		}
// 	}
//
//
// 	/**
// 	 * 设置缓存
// 	 *
// 	 * @param cacheName  缓存名
// 	 * @param keyPrefix  缓存键前缀
// 	 * @param key        缓存键值
// 	 * @param value      缓存值
// 	 */
// 	public static void put(String cacheName, String keyPrefix, Object key, @Nullable Object value) {
// 		getCache(cacheName).put(formatKey(keyPrefix, key), value);
// 	}
//
//
// 	/**
// 	 * 清除缓存
// 	 *
// 	 * @param cacheName  缓存名
// 	 * @param keyPrefix  缓存键前缀
// 	 * @param key        缓存键值
// 	 */
// 	public static void evict(String cacheName, String keyPrefix, Object key) {
// 		if (StringUtils.isEmpty(cacheName) || isEmpty(key)) {
// 			return;
// 		}
// 		getCache(cacheName).evict(formatKey(keyPrefix, key));
// 	}
//
//
// 	/**
// 	 * 清除缓存
// 	 *
// 	 * @param cacheName  缓存名
// 	 * @param key        缓存键值
// 	 */
// 	public static void evict(String cacheName, Object key) {
// 		if (StringUtils.isEmpty(cacheName) || isEmpty(key)) {
// 			return;
// 		}
// 		getCache(cacheName).evict(key);
// 	}
//
//
// 	/**
// 	 * 清空缓存 - 使用scan命令，避免keys命令阻塞Redis
// 	 *
// 	 * @param cacheName 缓存名
// 	 */
// 	public static void clear(String cacheName) {
// 		if (StringUtils.isEmpty(cacheName)) {
// 			return;
// 		}
//
// 		// 如果RedisTemplate可用，使用scan命令清除缓存
// 		if (redisTemplate != null) {
// 			clearByScan(cacheName + ":*");
// 		} else {
// 			// 降级使用Spring Cache的clear方法
// 			getCache(cacheName).clear();
// 		}
// 	}
//
// 	/**
// 	 * 清空缓存 - 使用scan命令，避免keys命令阻塞Redis
// 	 *
// 	 * @param cacheName 缓存名
// 	 * @param key 缓存键
// 	 */
// 	public static void clear(String cacheName, Object key) {
// 		if (StringUtils.isEmpty(cacheName)) {
// 			return;
// 		}
//
// 		// 格式化缓存名
// 		String cacheKey = formatKey(cacheName, key);
//
// 		// 如果RedisTemplate可用，使用scan命令清除缓存
// 		if (redisTemplate != null) {
// 			clearByScan(cacheKey + ":*");
// 		} else {
// 			// 降级使用Spring Cache的clear方法
// 			getCache(cacheKey).clear();
// 		}
// 	}
//
// 	/**
// 	 * 使用scan命令清除匹配模式的所有缓存
// 	 *
// 	 * @param pattern 匹配模式
// 	 */
// 	private static void clearByScan(String pattern) {
// 		ScanOptions scanOptions = ScanOptions.scanOptions()
// 				.match(pattern).count(1000).build();
// 		// 迭代游标并删除
// 		redisTemplate.execute(connection -> {
// 			Cursor<byte[]> cursor = connection.scan(scanOptions);
// 			List<String> keys = new ArrayList<>();
// 			while (cursor.hasNext()) {
// 				String key = new String(cursor.next(), StandardCharsets.UTF_8);
// 				keys.add(key);
// 				if (keys.size() >= 20) {
// 					redisTemplate.delete(new ArrayList<>(keys));
// 					keys.clear();
// 				}
// 			}
// 			if (!keys.isEmpty()) {
// 				redisTemplate.delete(new ArrayList<>(keys));
// 			}
// 			return null;
// 		}, true);
// 	}
//
// 	private static boolean isEmpty(@Nullable Object obj) {
// 		if (obj == null) {
// 			return true;
// 		} else if (obj instanceof Optional) {
// 			return !((Optional)obj).isPresent();
// 		} else if (obj instanceof CharSequence) {
// 			return ((CharSequence)obj).length() == 0;
// 		} else if (obj.getClass().isArray()) {
// 			return Array.getLength(obj) == 0;
// 		} else if (obj instanceof Collection) {
// 			return ((Collection)obj).isEmpty();
// 		} else {
// 			return obj instanceof Map ? ((Map)obj).isEmpty() : false;
// 		}
// 	}
// }
