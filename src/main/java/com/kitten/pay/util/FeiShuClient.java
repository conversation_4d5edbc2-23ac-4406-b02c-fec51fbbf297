package com.kitten.pay.util;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Slf4j
public class FeiShuClient {
    private static final String ALARM_URL = "https://open.feishu.cn/open-apis/bot/v2/hook/422d6f2f-374f-4dd6-bd00-80bf5bd0497d";

    private static final String TEMPLATE = "{\"msg_type\":\"post\",\"content\":{\"post\":{\"zh_cn\":{\"title\":\"%s\",\"content\":[[{\"tag\":\"at\",\"user_id\":\"all\"},{\"tag\":\"text\",\"text\":\"%s\"}]]}}}}";

    @Autowired
    private OkHttpClient okHttpClient;
    @Value("${spring.profiles.active}")
    private String profile;


    // private static final String MSG = "\nhost：ufo_live_admin1\nmsg：";
    private static final String MSG = "\nsource：kitten-pay\nhost：ufo_live_admin1\nmsg：";
    @Async
    public void alarm(String title, String content) {
        if (StringUtils.equals("prod", profile)) {
            title = "【正式环境】" + title;
        } else {
            title = "【测试环境】" + title;
        }
        JSONObject data = JSONObject.parseObject(String.format(TEMPLATE, title, MSG + content));
        JSONObject res = post(data, JSONObject.class);
        log.info("feishu response：{}", res);
    }

    private <T> T post(JSONObject data, Class<T> clazz) {
        try {
            RequestBody body = RequestBody.create(data.toJSONString(), MediaType.parse("application/json"));
            Map<String, String> headers = Maps.newHashMap();
            headers.put("content-type", "application/json");
            Request request = new Request.Builder()
                    .url(ALARM_URL).post(body).headers(Headers.of(headers))
                    .build();
            log.info("feishu request：{}，param：{}", request, data);
            try (Response response = okHttpClient.newCall(request).execute()) {
                String string = response.body().string();
                return JSONObject.parseObject(string, clazz);
            }
        } catch (Exception e) {
            log.error("feishu request error，param：{}", data, e);
            throw new RuntimeException(e);
        }
    }
}
