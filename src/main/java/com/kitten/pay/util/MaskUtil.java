package com.kitten.pay.util;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 参数名字根据不同包变换
 * 整体思想对字母进行移位处理，理论上有52*51*50*49*...*1种变换。
 * <AUTHOR>
 */
public class MaskUtil {
    static final String ORIGINAL_KEY= "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";

    static Map<Integer, Map<Character, Character>> encodeMask = new ConcurrentHashMap<>();

    static Map<Integer, Map<Character, Character>> decodeMask = new ConcurrentHashMap<>();


    public static int getOffset(String app) {
        if(StringUtils.isBlank(app)){
            return -1;
        }
        //算偏移量 52进制，a-z 为0-26，A-Z为27-52
        int offset = 0;
        for (int i = 0; i < app.length(); i++) {
            char c = app.charAt(i);
            if (c >= 'a' && c <= 'z') {
                offset = offset * 52 + c - 'a';
            } else if (c >= 'A' && c <= 'Z') {
                offset = offset * 52 + 26 + c - 'A';
            }else if(i!=0 && c== '/'){
                break;
            }
        }
        //app h5
        return (offset == 795 || offset== 7) ? 0 : offset;
    }


    private static void initKey(int offset){
        String  m = getMaskKey(ORIGINAL_KEY,offset);
        Map p_mask_key = new HashMap<>(m.length());
        for(int i=0; i<m.length(); i++){
            p_mask_key.put(ORIGINAL_KEY.charAt(i), m.charAt(i));
        }
        encodeMask.put(offset, p_mask_key);

    }


    private static void initValue(int offset){
        String  m = getMaskKey(ORIGINAL_KEY,offset);;
        Map p_mask_value = new HashMap<>(m.length());
        for(int i=0; i<m.length(); i++){
            p_mask_value.put(m.charAt(i), ORIGINAL_KEY.charAt(i));
        }
        decodeMask.put(offset, p_mask_value);
    }



    /**
     * 算法就是对0-52个字符移位 (52)* 对1-52个字符移位(51) * 对2-52个字符移位(51) ***** * 对52-52个字符移位(1)
     * 算法就是52种*51种 *****1种，对应52*51*50*49*...*1种变换。
     */
    private static String getMaskKey(String originalKey, int offset) {
        if (offset/originalKey.length()>0){
            int o = offset%originalKey.length();
            int movePoint = 52-originalKey.length()+1;
            originalKey = originalKey.substring(originalKey.length()-o)
                    + originalKey.substring(0,originalKey.length()-o);
            return originalKey.substring(originalKey.length()-movePoint)
                    + getMaskKey(originalKey.substring(0,originalKey.length()-movePoint), offset/originalKey.length());
        }else{
            return originalKey.substring(originalKey.length()-offset)
                    + originalKey.substring(0,originalKey.length()-offset);
        }
    }


    private static String replace(Map<Character,Character> mask, String key) {
        StringBuilder stringBuilder = new StringBuilder(key.length());
        for (int i = 0; i < key.length(); i++) {
            Character v = mask.get(key.charAt(i));
            stringBuilder.append(v==null?key.charAt(i):v);
        }
        return stringBuilder.toString();
    }

    private static char replace(Map<Character,Character> mask, char key) {
        Character v = mask.get(key);
        return v==null?key:v;
    }

    /**
     *加密函数
     */
    public static String encode(String key,int offset) {
        if (key==null || offset==0) {
            return key;
        }
        if (encodeMask.get(offset)==null) {
            initKey(offset);
        }
       return replace(encodeMask.get(offset), key);
    }

    /**
     *加密函数
     */
    public static char encode(char key,int offset) {
        if (encodeMask.get(offset)==null || offset==0) {
            initKey(offset);
        }
        return replace(encodeMask.get(offset), key);
    }

    /**
     *解密函数
     */
    public static String decode(String value,int offset) {
        if (value==null || offset==0) {
            return value;
        }
        if(decodeMask.get(offset)==null) {
            initValue(offset);
        }
        return replace(decodeMask.get(offset), value);
    }


    /**
     *解密函数
     */
    public static char decode(char value,int offset) {
        if(decodeMask.get(offset)==null || offset==0) {
            initValue(offset);
        }
        return replace(decodeMask.get(offset), value);
    }





    /**
     * 对Json key 进行mask处理的类
     * <AUTHOR>
     */
    public static class Param {

        /************************** 对Json key的转换 ****************************/
        public static boolean isJson(String str) {
            if(str==null ||str.length()<1){
                return false;
            }
            return str.startsWith("{") || str.startsWith("[");
        }

        public static String encodeJson(String json, int offset){
            if(offset==0 || json ==null){
                return json;
            }
            char []jsonChar = json.toCharArray();
            boolean key = false, keyPre = false;
            for(int i= jsonChar.length-1; i>0; i--){
                if(jsonChar[i] == '"' && jsonChar[i-1] != '\\'){
                    if(key){
                        key = false;
                    }else if(keyPre){
                        key = true;
                    }
                }else{
                    if(jsonChar[i] == ':'){
                        keyPre = true;
                    }else{
                        if(keyPre && jsonChar[i] != ' '){
                            keyPre = false;
                        }
                    }
                }
                if(key){
                    jsonChar[i] = MaskUtil.encode(jsonChar[i],offset);
                }
            }

            return new String(jsonChar);
        }

        public static String decodeJson(String json, int offset){
            if(offset==0 || json ==null){
                return json;
            }
            char []jsonChar = json.toCharArray();
            boolean key = false, keyPre = false;
            for(int i= jsonChar.length-1; i>0; i--){
                if(jsonChar[i] == '"' && jsonChar[i-1] != '\\'){
                    if(key){
                        key = false;
                    }else if(keyPre){
                        key = true;
                    }
                }else{
                    if(jsonChar[i] == ':'){
                        keyPre = true;
                    }else{
                        if(keyPre && jsonChar[i] != ' '){
                            keyPre = false;
                        }
                    }
                }
                if(key){
                    jsonChar[i] = MaskUtil.decode(jsonChar[i],offset);
                }
            }
            return new String(jsonChar);
        }

        /************************** 对Json key的转换 ****************************/

        /************************** 对form-data key的转换  ****************************/

        public static String encodeForm(String form, int offset) {
            if(offset==0 || form ==null){
                return form;
            }
            char []paramChar = form.toCharArray();
            boolean key = true;
            for(int i=0; i<paramChar.length; i++){
                if(paramChar[i] == '='){
                    key = false;
                }else if(paramChar[i] == '&'){
                    key = true;
                }
                if(key){
                    paramChar[i] = MaskUtil.encode(paramChar[i],offset);
                }
            }
            return new String(paramChar);
        }

        public static String decodeForm(String form, int offset) {
            if(offset==0 || form ==null){
                return form;
            }
            char []paramChar = form.toCharArray();
            boolean key = true;
            for(int i=0; i<paramChar.length; i++){
                if(paramChar[i] == '='){
                    key = false;
                }else if(paramChar[i] == '&'){
                    key = true;
                }
                if(key){
                    paramChar[i] = MaskUtil.decode(paramChar[i],offset);
                }
            }
            return new String(paramChar);
        }

        /************************** 对form-data key的转换 ****************************/

        /************************** 对url的转换 ****************************/

        public static String encodeUrl(String url, int offset){
            if(offset==0 || url ==null){
                return url;
            }
            if(url.startsWith("http")){
                int pathStar =  url.indexOf('/',10);
                String host = url.substring(0,pathStar);
                String path = url.substring(pathStar+1);
                String encodePath = path.substring(path.indexOf('/')+1);
                return host + "/" + Base52Util.encode(offset) + "/" + encodePath(encodePath, offset);
            }else{
                return encodePath(url,offset);
            }
        }

        public static String decodeUrl(String url, int offset){
            if(offset==0 || url ==null){
                return url;
            }
            if(url.startsWith("http")){
                int pathStar =  url.indexOf('/',10);
                String host = url.substring(0,pathStar);
                String path = url.substring(pathStar+1);
                String decodePath = path.substring(path.indexOf('/')+1);
                return host + "/app/" + decodePath(decodePath, offset);
            }else{
                return decodePath(url,offset);
            }
        }


        private static String encodePath(String path, int offset) {
            char []paramChar = path.toCharArray();
            boolean key = true;
            for(int i=0; i<paramChar.length; i++){
                if(paramChar[i] == '='){
                    key = false;
                }else if(paramChar[i] == '&'){
                    key = true;
                }
                if(key){
                    paramChar[i] = MaskUtil.encode(paramChar[i],offset);
                }
            }
            return new String(paramChar);
        }

        private static String decodePath(String path, int offset) {
            char []paramChar = path.toCharArray();
            boolean key = true;
            for(int i=0; i<paramChar.length; i++){
                if(paramChar[i] == '='){
                    key = false;
                }else if(paramChar[i] == '&'){
                    key = true;
                }
                if(key){
                    paramChar[i] = MaskUtil.decode(paramChar[i],offset);
                }
            }
            return new String(paramChar);
        }

        /************************** 对url的转换 ****************************/

    }


   /* public static void main(String []args) {
        ///dKC/W-YWGV/xYXL/UYMJO-PSKMR
        String url = "/dKC/W-YWGV/xYXL/UYMJO-PSKMR?xJJSYRX=9CF5D147-92F2-46B1-9EB7-571FD7CCD1CD&RxQG=pop26050&KGRFGV=1";
        int offset = MaskUtil.getOffset(url);

        //offset 代表包的代号，非正常包请求


        String newUrl = MaskUtil.Param.decodeUrl(url.toString(), offset);
        System.out.println(getOffset("dKC"));
    }*/

  /*public static void main(String []args){
      System.out.println(getOffset("hww"));
      System.out.println(MaskUtil.Param.decodeUrl("https://api.mokichat.com/hww/z-bzly/hbao/svnpu",20094));
      String urlParam1 = "CRRkF=ios-com.demo.roar";
      System.out.println(MaskUtil.Param.decodeForm(urlParam1,24));
      String uri = "http://api.ddd.com/app/s-basic/project/app?app=2&file=2";
      System.out.println(MaskUtil.Param.encodeUrl(uri,60071));
      String uri1 = "http://api.ddd.com/wll/o-xwoey/lnkfayp/wll?wll=2&beha=2";
      System.out.println(MaskUtil.Param.decodeUrl(uri1,60071));
      String json =  "{\n" +
              "    \"whatsApp\" : \"6582548229\",\n" +
              "    \"greetTrick\"  : [{\n" +
              "        \"female\": [\"https://apps.apple.com/us/app/moki-video-chat-live-talk/id1627445302\",\"https://apps.apple.com/us/app/moki-video-chat-live-talk/id1627445302\"],\n" +
              "        \"male\": [\"https://apps.apple.com/us/app/moki-video-chat-live-talk/id1627445302\",\"https://apps.apple.com/us/app/moki-video-chat-live-talk/id1627445302\"]\n" +
              "    }],\n" +
              "    \"callRecommend\": \"y\",\n" +
              "    \"verbalTrick\": {\n" +
              "        \"female\": [\"https://apps.apple.com/us/app/moki-video-chat-live-talk/id1627445302\",\"https://apps.apple.com/us/app/moki-video-chat-live-talk/id1627445302\"],\n" +
              "        \"male\": [\"https://apps.apple.com/us/app/moki-video-chat-live-talk/id1627445302\",\"https://apps.apple.com/us/app/moki-video-chat-live-talk/id1627445302\"]\n" +
              "    },\n" +
              "    \"cdnUrl\": \"https://star-app.s3.us-east-2.amazonaws.com/\",\n" +
              "    \"match2auto\": 2,\n" +
              "    \"serverTime\": 1664437566239,\n" +
              "    \"recommend\": {\n" +
              "        \"connectTimes\": 2,\n" +
              "        \"waitTime\": 5\n" +
              "    },\n" +
              "    \"version\": {\n" +
              "        \"downUrl\": \"https://apps.apple.com/us/app/moki-video-chat-live-talk/id1627445302\",\n" +
              "        \"appDesc\": \"What's New:\\nLive video chat with your friends.\\nSend gifts to anyone who you are interested in.\\nImproved performance and user experience.\",\n" +
              "        \"version\": \"1.,,,8.0\",\n" +
              "        \"status\": \"0\"\n" +
              "    }\n" +
              "}";
      String encodeJson =  MaskUtil.Param.encodeJson(json,5);
      System.out.println(encodeJson);
      System.out.println("=============================================="+ getOffset("wll"));
      System.out.println(MaskUtil.Param.decodeJson(encodeJson,5));

  }*/

}
