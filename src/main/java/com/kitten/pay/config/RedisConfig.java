// package com.kitten.pay.config;
//
// import com.baomidou.mybatisplus.core.toolkit.StringPool;
// import com.fasterxml.jackson.annotation.JsonTypeInfo;
// import com.fasterxml.jackson.databind.DeserializationFeature;
// import com.fasterxml.jackson.databind.ObjectMapper;
// import com.fasterxml.jackson.databind.SerializationFeature;
// import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
// import com.kitten.pay.util.CacheUtil;
// import com.kitten.pay.util.RedisUtil;
// import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
// import org.springframework.boot.autoconfigure.AutoConfigureBefore;
// import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
// import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
// import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
// import org.springframework.context.annotation.Bean;
// import org.springframework.context.annotation.Configuration;
// import org.springframework.data.redis.cache.RedisCacheConfiguration;
// import org.springframework.data.redis.cache.RedisCacheManager;
// import org.springframework.data.redis.cache.RedisCacheWriter;
// import org.springframework.data.redis.connection.RedisConnectionFactory;
// import org.springframework.data.redis.connection.RedisPassword;
// import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
// import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
// import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
// import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
// import org.springframework.data.redis.core.RedisTemplate;
// import org.springframework.data.redis.core.StringRedisTemplate;
// import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
// import org.springframework.data.redis.serializer.RedisSerializationContext;
// import org.springframework.data.redis.serializer.RedisSerializer;
// import org.springframework.data.redis.serializer.StringRedisSerializer;
//
// import java.time.Duration;
//
// @Configuration
// @AutoConfigureBefore(RedisAutoConfiguration.class)
// public class RedisConfig {
//
//     @Bean
//     public LettuceConnectionFactory lettuceConnectionFactory(RedisProperties redisProperties) {
//         GenericObjectPoolConfig<?> genericObjectPoolConfig = new GenericObjectPoolConfig<>();
//         RedisProperties.Pool pool = redisProperties.getLettuce().getPool();
//         genericObjectPoolConfig.setMaxIdle(pool.getMaxIdle());
//         genericObjectPoolConfig.setMinIdle(pool.getMinIdle());
//         genericObjectPoolConfig.setMaxTotal(pool.getMaxActive());
//         genericObjectPoolConfig.setMinEvictableIdleTimeMillis(pool.getTimeBetweenEvictionRuns().toMillis());
//         RedisStandaloneConfiguration redisStandaloneConfiguration = new RedisStandaloneConfiguration();
//         redisStandaloneConfiguration.setDatabase(redisProperties.getDatabase());
//         redisStandaloneConfiguration.setHostName(redisProperties.getHost());
//         redisStandaloneConfiguration.setPort(redisProperties.getPort());
//         redisStandaloneConfiguration.setPassword(RedisPassword.of(redisProperties.getPassword()));
//         LettuceClientConfiguration clientConfig = LettucePoolingClientConfiguration.builder()
//                 //.commandTimeout(redisProperties.getTimeout())
//                 //.shutdownTimeout(redisProperties.getLettuce().getShutdownTimeout())
//                 .poolConfig(genericObjectPoolConfig)
//                 .build();
//         return new LettuceConnectionFactory(redisStandaloneConfiguration, clientConfig);
//     }
//
//
//     @Bean(name = "redisTemplate")
//     public RedisTemplate<Object, Object> redisTemplate(LettuceConnectionFactory lettuceConnectionFactory, RedisSerializer<Object> redisSerializer) {
//         RedisTemplate<Object, Object> redisTemplate = new RedisTemplate<>();
//         redisTemplate.setConnectionFactory(lettuceConnectionFactory);
//         redisTemplate.setKeySerializer(StringRedisSerializer.UTF_8);
//         redisTemplate.setValueSerializer(redisSerializer);
//         redisTemplate.setHashKeySerializer(redisSerializer);
//         redisTemplate.setHashValueSerializer(redisSerializer);
//         redisTemplate.afterPropertiesSet();
//         return redisTemplate;
//     }
//
//     @Bean
//     public StringRedisTemplate stringRedisTemplate(LettuceConnectionFactory lettuceConnectionFactory) {
//         return new StringRedisTemplate(lettuceConnectionFactory);
//     }
//
//     @Bean
//     @ConditionalOnMissingBean
//     public RedisSerializer<Object> redisSerializer() {
//
//         ObjectMapper objectMapper = new ObjectMapper();
//         objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
//         objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
//         objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
//         objectMapper.configure(DeserializationFeature.FAIL_ON_INVALID_SUBTYPE, false);
//         //不适用默认的dateTime进行序列化,使用JSR310的LocalDateTimeSerializer
//         objectMapper.configure(SerializationFeature.WRITE_DATE_KEYS_AS_TIMESTAMPS, false);
//         //重点,这是序列化LocalDateTIme和LocalDate的必要配置,由Jackson-data-JSR310实现
//         //objectMapper.registerModule(new JavaTimeModule());
//         //必须配置,有兴趣参考源码解读
//         objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL, JsonTypeInfo.As.PROPERTY);
//         GenericJackson2JsonRedisSerializer.registerNullValueSerializer(objectMapper, null);
//         return new GenericJackson2JsonRedisSerializer(objectMapper);
//
//     }
//
//     @Bean
//     @ConditionalOnMissingBean
//     public RedisCacheConfiguration redisCacheConfiguration(RedisSerializer<Object> redisSerializer) {
//         RedisSerializationContext.SerializationPair<Object> pair = RedisSerializationContext.SerializationPair.fromSerializer(redisSerializer);
//         return RedisCacheConfiguration.defaultCacheConfig().computePrefixWith(name -> name + StringPool.COLON)
//                 .entryTtl(Duration.ofDays(1))
//                 .serializeValuesWith(pair);
//     }
//
//     @Bean
//     public RedisCacheManager cacheManager(RedisConnectionFactory redisConnectionFactory, RedisCacheConfiguration redisCacheConfiguration, RedisTemplate<Object, Object> redisTemplate) {
//         //初始化一个RedisCacheWriter
//         RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);
//         //初始化RedisCacheManager
//         RedisCacheManager redisCacheManager = new RedisCacheManager(redisCacheWriter, redisCacheConfiguration);
//         CacheUtil.init(redisCacheManager, redisTemplate);
//         return redisCacheManager;
//     }
//
//     @Bean
//     public RedisUtil redisUtil(RedisTemplate<Object, Object> redisTemplate) {
//         return new RedisUtil(redisTemplate);
//     }
//
// }
