package com.kitten.pay.config.properties;

import com.kitten.pay.config.YamlPropertySourceFactory;
import lombok.Data;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@ConfigurationProperties(prefix = "pay")
@PropertySource(value = "classpath:/pay/config.yml", factory = YamlPropertySourceFactory.class)
@Setter
public class PayProperties {
    List<AppleConfig> apple;
    Map<String, String> google;
    private Map<String, AppleConfig> appleTmpMap;

    @PostConstruct
    public void init() {
        appleTmpMap = apple.stream().collect(Collectors.toMap(AppleConfig::getPkg, c -> c));
    }


    @Data
    public static class AppleConfig {
        private String pkg;
        public String sandboxUrl;
        public String verifyUrl;
        private String password;
    }

    public AppleConfig getAppleConfig(String pkg) {
        return appleTmpMap.get(pkg);
    }

    public String getGoogleKey(String pkg) {
        return google.get(pkg);
    }
}
