package com.kitten.pay.controller.other;

import com.kitten.pay.dto.res.PayNotifyRes;
import com.kitten.pay.service.PayService;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

@RestController
@RequestMapping("/notify")
public class NotifyController {
    @Autowired
    private PayService payService;


    @PostMapping(value = "/google")
    public PayNotifyRes googleNotify(@RequestBody String s) {
        try {
            MDC.put("traceId", UUID.randomUUID().toString());
            payService.googleNotify(s);
        } finally {
            MDC.clear();
        }

        return new PayNotifyRes();
    }

    @PostMapping(value = "/apple")
    public PayNotifyRes appleNotify(@RequestBody String s) {
        try {
            MDC.put("traceId", UUID.randomUUID().toString());
            payService.appleNotify(s);
        } finally {
            MDC.clear();
        }
        return new PayNotifyRes();
    }
}
