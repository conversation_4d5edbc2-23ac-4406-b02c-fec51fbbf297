package com.kitten.pay.controller.app;

import com.kitten.pay.anno.doc.Doc;
import com.kitten.pay.dto.req.ApplePayReq;
import com.kitten.pay.dto.req.GooglePayReq;
import com.kitten.pay.dto.res.R;
import com.kitten.pay.service.PayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("/app/pay")
public class PayController {
    @Autowired
    private PayService payService;

    @Doc(
            title = "谷歌支付",
            catalog = "支付",
            number = "1"
    )
    @PostMapping(value = "/google-pay")
    public R<Void> googlePay(@RequestBody @Valid GooglePayReq req) {
        payService.googlePay(req);
        return R.success();
    }

    @Doc(
            title = "苹果支付",
            catalog = "支付",
            number = "2"
    )
    @PostMapping(value = "/apple-pay")
    public R<Void> applePay(@RequestBody @Valid ApplePayReq req) {
        payService.applePay(req);
        return R.success();
    }

}
