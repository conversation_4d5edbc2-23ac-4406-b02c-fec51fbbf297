package com.kitten.pay.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName("payments_billingv2")
@Data
public class PaymentsBillingv2 {
    private Long id;

    private String orderNo;

    private String transactionId;

    private String originalTransactionId;

    private String productId;

    private BigDecimal productPrice;

    private Integer coins;

    private Integer userUid;

    private Integer userId;

    private Short status;

    private Short channel;

    private String receipt;

    private String extraInfo;

    private Boolean firstPay;

    private Boolean isSandbox;

    private Integer beforeCoins;

    private Integer afterCoins;

    private String goodsUid;

    private String pricingUid;

    private String templateUid;

    private Short templateType;

    private String originalGoodsUid;

    private Integer originalCoins;

    private String discountGoodsUid;

    private Integer discountCoins;

    private Boolean discountActive;

    private Integer discountRate;

    private Integer days;

    private Long startTs;

    private Long expireTs;

    private Boolean isInVideoBilling;

    private Boolean isAnchorLinkBilling;

    private Integer anchorUid;

    private Short rechargeChannel;

    private String country;

    private Short devOs;

    private Boolean isNewUser;

    private Boolean isHidden;

    private Boolean restoreHidden;

    private BigDecimal sourceIncome;

    private String sourceCode;

    private Date createTime;

    private Date updateTime;
}