package com.kitten.pay.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kitten.pay.entity.PaymentsBillingv2;
import com.kitten.pay.mapper.PaymentsBillingv2Mapper;
import com.kitten.pay.service.PaymentsBillingv2Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class PaymentsBillingv2ServiceImpl implements PaymentsBillingv2Service {
    @Autowired
    private PaymentsBillingv2Mapper paymentsBillingv2Mapper;

    @Override
    public PaymentsBillingv2 selectByOrderNo(String orderNo) {
        LambdaQueryWrapper<PaymentsBillingv2> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PaymentsBillingv2::getOrderNo, orderNo);
        return paymentsBillingv2Mapper.selectOne(queryWrapper);
    }
}
