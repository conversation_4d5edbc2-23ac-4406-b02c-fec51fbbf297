package com.kitten.pay.service.impl.processor;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.kitten.pay.common.constants.Constants;
import com.kitten.pay.config.properties.PayProperties;
import com.kitten.pay.dto.req.ApplePayReq;
import com.kitten.pay.dto.req.ReceiptVerifyReq;
import com.kitten.pay.dto.ReceiptInfo;
import com.kitten.pay.exception.AppExceptionEnum;
import com.kitten.pay.util.ApplePayUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;

import java.util.List;

@Component
@Slf4j
public class AppleReceiptProcessor extends AbstractReceiptProcessor<ApplePayReq> {
    @Autowired
    private PayProperties payProperties;

    @Override
    public List<ReceiptInfo> extractReceipt(ReceiptVerifyReq<ApplePayReq> req) {
        ApplePayReq param = req.getParam();
        String pkg = req.getPkg();
        Long uid = req.getUid();
        String receipt = param.getReceipt();
        log.info("【{}】Apple支付请求参数：{}", uid, param);
        PayProperties.AppleConfig appleConfig = payProperties.getAppleConfig(pkg);
        if (appleConfig == null) {
            log.error("【{}】未配置该包的支付配置", pkg);
            throw AppExceptionEnum.PayException.ORDER_CHECK_FAIL;
        }
        log.info("appId：{}，appleKey为：{}", pkg, appleConfig.getPassword());
        String verifyResult = ApplePayUtil.buyAppVerify(pkg, appleConfig.getVerifyUrl(), receipt, appleConfig.getPassword());
        log.info("【{}--{}】线上环境返回JSON：{}", uid, appleConfig.getPassword(), verifyResult);
        // 发送平台验证
        // 连接苹果服务器异常
        if (verifyResult == null) {
            log.info("无订单信息!");
            return null;
        }
        JSONObject job = JSONObject.parseObject(verifyResult);
        if (job == null) {
            log.error("苹果支付服务器返回结果为空");
            throw AppExceptionEnum.PayException.ORDER_CHECK_FAIL;
        }
        int states = job.getIntValue("status");
        // 是沙盒环境，应沙盒测试，否则执行下面
        //21000 对 App Store 的请求不是使用 HTTP POST 请求方法发出的。
        //21001 App Store 不再发送此状态代码。
        //21002 receipt-data属性中的数据格式错误或服务遇到临时问题。再试一次。
        //21003 收据无法验证。
        //21004 您提供的共享机密与您帐户中存档的共享机密不匹配。
        //21005 收据服务器暂时无法提供收据。再试一次。
        //21006 此收据有效，但订阅已过期。当此状态代码返回到您的服务器时，接收数据也会被解码并作为响应  的一部分返回。仅针对自动续订订阅的 iOS 6 样式交易收据返回。
        //21007 这个收据是来自测试环境，但是是送到生产环境去验证的。
        //21008 这个收据是来自生产环境，但是被送到了测试环境进行验证。
        //21009 内部数据访问错误。稍后再试。
        //21010 用户帐户无法找到或已被删除。
        boolean isSandbox = false;
        if (21007 == states) {
            // 2.再沙盒测试
            verifyResult = ApplePayUtil.buyAppVerify(pkg, appleConfig.getSandboxUrl(), receipt, appleConfig.getPassword());
            // 发送平台验证
            log.info("【{}】沙盒环境返回JSON：{}", uid, verifyResult);
            job = JSONObject.parseObject(verifyResult);
            if (job == null) {
                log.error("苹果支付服务器返回结果为空");
                throw AppExceptionEnum.PayException.ORDER_CHECK_FAIL;
            }
            states = job.getIntValue("status");
            isSandbox = true;
        }
        // 前端所提供的收据是有效的 验证成功
        if (states == 0) {
            JSONObject returnJson = job.getJSONObject("receipt");
            String bundleId = returnJson.getString("bundle_id");
            JSONArray inApp = returnJson.getJSONArray("in_app");
            int size = inApp.size();
            log.info("【{}--{}】根据收据获取到的支付结果数量为：{}", uid, receipt, size);
            int successNum = 0;
            List<ReceiptInfo> objects = Lists.newArrayList();
            if (size > 0) {
                for (int i = 0; i < size; i++) {
                    JSONObject obj = inApp.getJSONObject(i);
                    String productId = obj.getString("product_id");
                    Long purchaseDateMs = obj.getLong("purchase_date_ms");
                    // 订单号
                    String transactionId = obj.getString("transaction_id");
                    // 原始订单号
                    String originalTransactionId = obj.getString("original_transaction_id");
                    //过期时间
                    String expiresDateMs = obj.getString("expires_date_ms") == null ? "" : obj.getString("expires_date_ms");
                    log.info("【{}】Apple支付：productId：{}，transactionId：{}，originalTransactionId：{}，expiresDateMs：{}", uid, productId, transactionId, originalTransactionId, expiresDateMs);
                    // 过期时间不为空，是订阅类型的
                    ReceiptInfo result = new ReceiptInfo();
                    result.setSandbox(isSandbox);
                    result.setPurchaseTime(purchaseDateMs);
                    result.setPackageName(bundleId);
                    if (StringUtils.isNotBlank(expiresDateMs)) {
                        long expireTime = Long.parseLong(expiresDateMs);
                        result.setExpiryTimeMillis(expireTime);
                    }
                    successNum++;
                    result.setProductId(productId);
                    result.setTransactionId(transactionId);
                    result.setOriginalTransactionId(originalTransactionId);
                    objects.add(result);
                }
            }
            if (successNum > 0) {
                log.info("【{}-{}】Apple支付结果验证通过", uid, receipt);
                return objects;
            }
        }
        log.info("【{}】Apple支付结果验证不通过：{}", uid, states);
        return null;
    }

    @Override
    protected ReceiptInfo notify(JSONObject notify) {
        log.info("苹果服务器v2通知请求参数：{}", notify);
        // 1. 先进行jws解密，转换成json后再进行业务处理
        String signedPayload = notify.getString("signedPayload");
        if (StringUtils.isBlank(signedPayload)) {
            log.error("苹果服务器通知请求参数【signedPayload】为空");
            return null;
        }
        JSONObject payload = decodeSign(signedPayload);
        String notificationType = payload.getString("notificationType");
        log.info("苹果服务器通知v2的【notificationType】为：{}，通知参数解密后为：{}", notificationType, payload);
        if (StringUtils.equalsIgnoreCase(notificationType, Constants.AppleNotificationTypeV2.DID_RENEW)) {
            JSONObject data = payload.getJSONObject("data");
            JSONObject renewalInfo = decodeSign(data.getString("signedRenewalInfo"));
            log.info("苹果服务器通知的【{}-signedRenewalInfo】解密后为：{}", notificationType, renewalInfo);
            JSONObject transactionInfo = decodeSign(data.getString("signedTransactionInfo"));
            log.info("苹果服务器通知的【{}-signedTransactionInfo】解密后为：{}", notificationType, transactionInfo);
            String productCode = renewalInfo.getString("autoRenewProductId");
            String transactionId = transactionInfo.getString("transactionId");
            String originalTransactionId = transactionInfo.getString("originalTransactionId");
            Long expiresDateMs = transactionInfo.getLong("expiresDate");
            ReceiptInfo result = new ReceiptInfo();
            result.setOriginalTransactionId(originalTransactionId);
            result.setType(ReceiptInfo.RENEW);
            result.setTransactionId(transactionId);
            result.setExpiryTimeMillis(expiresDateMs);
            result.setProductId(productCode);
            result.setSandbox(!StringUtils.equalsIgnoreCase(transactionInfo.getString("environment"), "Production"));
            return result;
        } else if (StringUtils.equalsIgnoreCase(notificationType, Constants.AppleNotificationTypeV2.EXPIRED)) {
            JSONObject data = payload.getJSONObject("data");
            JSONObject transactionInfo = decodeSign(data.getString("signedTransactionInfo"));
            log.info("苹果服务器通知的【EXPIRED-signedTransactionInfo】解密后为：{}", transactionInfo);
            String originalTransactionId = transactionInfo.getString("originalTransactionId");
            ReceiptInfo result = new ReceiptInfo();
            result.setOriginalTransactionId(originalTransactionId);
            result.setPackageName(transactionInfo.getString("bundleId"));
            result.setType(ReceiptInfo.EXPIRE);
            result.setPayType(ReceiptInfo.APPLE);
            result.setExpiryTimeMillis(transactionInfo.getLong("expiresDate"));
            result.setTransactionId(transactionInfo.getString("transactionId"));
            result.setOriginalTransactionId(transactionInfo.getString("originalTransactionId"));
            result.setSandbox(!StringUtils.equalsIgnoreCase(transactionInfo.getString("environment"), "Production"));
            return result;
        } else if (StringUtils.equalsIgnoreCase(notificationType, Constants.AppleNotificationTypeV2.REFUND)) {
            JSONObject data = payload.getJSONObject("data");
            String signedTransactionInfo = data.getString("signedTransactionInfo");
            if (StringUtils.isNotBlank(signedTransactionInfo)) {
                JSONObject transactionInfo = decodeSign(signedTransactionInfo);
                log.info("苹果服务器通知的【REFUND-transactionInfo】解密后为：{}", transactionInfo);
                ReceiptInfo result = new ReceiptInfo();
                result.setPackageName(transactionInfo.getString("bundleId"));
                result.setPayType(ReceiptInfo.APPLE);
                result.setType(ReceiptInfo.REFUND);
                result.setQuantity(transactionInfo.getInteger("quantity"));
                result.setPurchaseTime(transactionInfo.getLong("purchaseDate"));
                result.setTransactionId(transactionInfo.getString("transactionId"));
                result.setOriginalTransactionId(transactionInfo.getString("originalTransactionId"));
                result.setSandbox(!StringUtils.equalsIgnoreCase(transactionInfo.getString("environment"), "Production"));
            }

        } else {
            log.info("【{}】不属于【DID_RENEW、SUBSCRIBED、EXPIRED】，目前仅做日志打印处理", notificationType);
            JSONObject data = payload.getJSONObject("data");
            String signedRenewalInfo = data.getString("signedRenewalInfo");
            if (StringUtils.isNotBlank(signedRenewalInfo)) {
                JSONObject renewalInfo = decodeSign(signedRenewalInfo);
                log.info("苹果服务器通知【{}-signedRenewalInfo】解密后为：{}", notificationType, renewalInfo);
            }
            String signedTransactionInfo = data.getString("signedTransactionInfo");
            if (StringUtils.isNotBlank(signedTransactionInfo)) {
                JSONObject transactionInfo = decodeSign(signedTransactionInfo);
                log.info("苹果服务器通知的【{}-transactionInfo】解密后为：{}", notificationType, transactionInfo);
            }
        }
        return null;
    }

    private JSONObject decodeSign(String data) {
        String[] split = data.split("\\.");
        String s = new String(Base64Utils.decodeFromString(split[1]));
        return JSONObject.parseObject(s);
    }
}
