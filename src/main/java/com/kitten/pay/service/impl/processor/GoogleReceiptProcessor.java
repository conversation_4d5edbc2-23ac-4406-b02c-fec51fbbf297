package com.kitten.pay.service.impl.processor;

import com.alibaba.fastjson2.JSONObject;
import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.jackson2.JacksonFactory;
import com.google.api.services.androidpublisher.AndroidPublisher;
import com.google.api.services.androidpublisher.AndroidPublisherScopes;
import com.google.api.services.androidpublisher.model.ProductPurchase;
import com.google.api.services.androidpublisher.model.SubscriptionPurchase;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kitten.pay.common.constants.Constants;
import com.kitten.pay.config.properties.PayProperties;
import com.kitten.pay.dto.req.GooglePayReq;
import com.kitten.pay.dto.req.ReceiptVerifyReq;
import com.kitten.pay.dto.ReceiptInfo;
import com.kitten.pay.exception.AppExceptionEnum;
import com.kitten.pay.util.Base64Util;
import com.kitten.pay.util.GooglePayUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.text.MessageFormat;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class GoogleReceiptProcessor extends AbstractReceiptProcessor<GooglePayReq> {
    @Autowired
    private PayProperties payProperties;


    @Override
    public List<ReceiptInfo> extractReceipt(ReceiptVerifyReq<GooglePayReq> req) {
        GooglePayReq param = req.getParam();
        String pkg = req.getPkg();
        Long uid = req.getUid();
        log.info("[GooglePay验证] 开始验证支付请求，用户ID：{}，请求参数：{}", uid, param);
        String key = payProperties.getGoogleKey(pkg);
        if (StringUtils.isBlank(key)) {
            log.error("[GooglePay验证] 获取googleKey失败，appId：{}", pkg);
            throw AppExceptionEnum.PayException.ORDER_CHECK_FAIL;
        }
        log.info("[GooglePay验证] 获取googleKey成功，appId：{}", pkg);

        boolean verifyPurchase = GooglePayUtil.verifyPurchase(key, param.getSignedData(), param.getSignature());
        log.info("[GooglePay验证] 签名验证结果：{}，用户ID：{}", verifyPurchase, uid);
        if (!verifyPurchase) {
            log.error("[GooglePay验证] 签名验证失败，用户ID：{}", uid);
            throw AppExceptionEnum.PayException.ORDER_CHECK_FAIL;
        }

        JSONObject job = JSONObject.parseObject(param.getSignedData());
        String states = job.getString("purchaseState");
        String appPackageName = job.getString("packageName");
        String productId = job.getString("productId");
        String orderId = job.getString("orderId");
        long purchaseTime = job.getLong("purchaseTime");
        String purchaseToken = job.getString("purchaseToken");
        Boolean acknowledged = job.getBoolean("acknowledged");
        boolean autoRenewing = job.getBoolean("autoRenewing") != null && job.getBoolean("autoRenewing");
        log.info("[GooglePay验证] 解析订单数据，states：{}，packageName：{}，productId：{}，orderId：{}，purchaseTime：{}，autoRenewing：{}，acknowledged：{}",
                states, appPackageName, productId, orderId, purchaseTime, autoRenewing, acknowledged);

        if ("0".equals(states) && !acknowledged) {
            log.info("[GooglePay验证] 获取商品信息成功，商品代码：{}", productId);
            ReceiptInfo result = new ReceiptInfo();
            result.setTransactionId(orderId);
            result.setPurchaseTime(purchaseTime);
            result.setPackageName(appPackageName);
            result.setProductId(productId);
            String[] split = orderId.split("\\.\\.");
            result.setOriginalTransactionId(split[0]);
            AndroidPublisher publisher = getPublisherByPackage(appPackageName);
            if (publisher == null) {
                log.error("[GooglePay验证] 获取AndroidPublisher失败，packageName：{}", appPackageName);
                throw AppExceptionEnum.PayException.ORDER_CHECK_FAIL;
            }
            if (autoRenewing) {
                log.info("[GooglePay验证] 订阅商品，开始获取订阅信息，packageName：{}，productId：{}，purchaseToken：{}", appPackageName, productId, purchaseToken);
                AndroidPublisher.Purchases.Subscriptions subscriptions = publisher.purchases().subscriptions();
                SubscriptionPurchase subscriptionPurchase = null;
                try {
                    subscriptionPurchase = subscriptions.get(appPackageName, productId, purchaseToken).execute();
                } catch (Exception e) {
                    log.error("[GooglePay验证] 获取订阅信息异常，packageName：{}，productId：{}，purchaseToken：{}", appPackageName, productId, purchaseToken, e);
                    throw AppExceptionEnum.CommonException.COMMON_ERROR;
                }
                if (subscriptionPurchase == null) {
                    log.error("[GooglePay验证] 获取订阅信息为空");
                    throw AppExceptionEnum.PayException.ORDER_CHECK_FAIL;
                }
                log.info("[GooglePay验证] 获取订阅信息成功，orderId：{}，paymentState：{}，autoRenewing：{}，expiryTime：{}",
                        subscriptionPurchase.getOrderId(),
                        subscriptionPurchase.getPaymentState(),
                        subscriptionPurchase.getAutoRenewing(),
                        subscriptionPurchase.getExpiryTimeMillis());
                Long expireTime = subscriptionPurchase.getExpiryTimeMillis();
                String linkedPurchaseToken = subscriptionPurchase.getLinkedPurchaseToken();
                // 订阅逻辑。。。。。
                result.setType(ReceiptInfo.SUBSCRIPTION_PURCHASED);
                result.setExpiryTimeMillis(expireTime);
                log.info("[GooglePay验证] 设置订阅信息，过期时间：{}，关联purchaseToken：{}", expireTime, linkedPurchaseToken);
            } else {
                AndroidPublisher.Purchases.Products products = publisher.purchases().products();
                try {
                    ProductPurchase purchase = products.get(appPackageName, productId, purchaseToken).execute();
                    if (purchase == null) {
                        log.error("[GooglePay验证] 获取支付信息为空");
                        throw AppExceptionEnum.PayException.ORDER_CHECK_FAIL;
                    }
                    log.info("[GooglePay验证] 获取订阅信息成功，orderId：{}，purchaseState：{}，obfuscatedExternalAccountId：{}，purchaseType：{}，quantity：{}",
                            purchase.getOrderId(),
                            purchase.getPurchaseState(),
                            purchase.getObfuscatedExternalAccountId(),
                            purchase.getPurchaseType(), purchase.getQuantity());
                    result.setPurchaseTime(purchase.getPurchaseTimeMillis());
                    result.setType(ReceiptInfo.ONE_TIME_PRODUCT_PURCHASED);
                    result.setQuantity(purchase.getQuantity());
                    result.setSandbox(purchase.getPurchaseType() != null);
                } catch (IOException e) {
                    throw AppExceptionEnum.PayException.ORDER_CHECK_FAIL;
                }
            }
            log.info("[GooglePay验证] 验证成功，返回结果：{}", result);
            return Lists.newArrayList(result);
        } else {
            log.warn("[GooglePay验证] 订单状态不正确或已确认，states：{}，acknowledged：{}", states, acknowledged);
        }
        return null;
    }

    public static Map<String, AndroidPublisher> publisherMap = Maps.newConcurrentMap();

    private AndroidPublisher getPublisherByPackage(String packageName) {
        AndroidPublisher publisher = publisherMap.get(packageName);
        if (publisher != null) {
            return publisher;
        } else {
            publisher = getAndroidPublisher(packageName);
            if (publisher != null) {
                publisherMap.put(packageName, publisher);
                return publisher;
            }
            return null;
        }
    }

    private static AndroidPublisher getAndroidPublisher(String packageName) {
        String keyFile = MessageFormat.format("pay/google/{0}.json", packageName);
        log.info("keyFile:{}", keyFile);
        InputStream serviceAccount = GoogleReceiptProcessor.class.getClassLoader().getResourceAsStream(keyFile);
        try {
            GoogleCredential credential = GoogleCredential.fromStream(serviceAccount).createScoped(AndroidPublisherScopes.all());
            if (credential != null) {
                // 其中这两个参数最终也没去搞明白,不知道具体的用处，目测可能是调用googleAPI发送请求用的
                NetHttpTransport httpTransport = GoogleNetHttpTransport.newTrustedTransport();
                JacksonFactory jacksonFactory = JacksonFactory.getDefaultInstance();
                return new AndroidPublisher.Builder(httpTransport, jacksonFactory, credential).build();
            }
        } catch (Exception e) {
            log.error("getAndroidPublisher fails {}", e.getMessage());

        }
        return null;
    }

    @Override
    protected ReceiptInfo notify(JSONObject param) {
        log.info("[GooglePay通知] 收到谷歌服务器通知，原始参数：{}", param);
        ReceiptInfo result = new ReceiptInfo();
        JSONObject message = param.getJSONObject("message");
        String data = message.getString("data");
        JSONObject obj = JSONObject.parseObject(new String(Base64Util.decode(data)));
        log.info("[GooglePay通知] 解密后的数据：{}", obj);
        String packageName = obj.getString("packageName");
        String eventTimeMillis = obj.getString("eventTimeMillis");
        log.info("[GooglePay通知] 包名：{}，事件时间：{}", packageName, eventTimeMillis);

        JSONObject subscriptionNotification = obj.getJSONObject("subscriptionNotification");
        if (subscriptionNotification == null) {
            log.info("[GooglePay通知] 非订阅通知，转为处理一次性商品通知");
            return processOnetimeProductNotify(obj, packageName);
        }

        Integer notificationType = subscriptionNotification.getInteger("notificationType");
        String purchaseToken = subscriptionNotification.getString("purchaseToken");
        String subscriptionId = subscriptionNotification.getString("subscriptionId");
        log.info("[GooglePay通知] 订阅通知类型：{}，purchaseToken：{}，subscriptionId：{}", notificationType, purchaseToken, subscriptionId);
        if (notificationType == null) {
            log.warn("[GooglePay通知] 谷歌推送缺少通知类型字段，不属于关联业务，跳过处理");
            return null;
        }
        AndroidPublisher publisher = getPublisherByPackage(packageName);
        if (publisher == null) {
            log.error("[GooglePay通知] 获取AndroidPublisher失败，packageName：{}", packageName);
            return null;
        }

        AndroidPublisher.Purchases.Subscriptions subscriptions = publisher.purchases().subscriptions();
        try {
            log.info("[GooglePay通知] 开始查询订阅信息，packageName：{}，subscriptionId：{}，purchaseToken：{}", packageName, subscriptionId, purchaseToken);
            SubscriptionPurchase subscriptionPurchase = subscriptions.get(packageName, subscriptionId, purchaseToken).execute();
            if (subscriptionPurchase == null) {
                log.error("[GooglePay通知] 获取subscriptionPurchase信息为空");
                return null;
            }
            log.info("[GooglePay通知] 获取订阅信息成功，orderId：{}，paymentState：{}，autoRenewing：{}，expiryTime：{}",
                    subscriptionPurchase.getOrderId(),
                    subscriptionPurchase.getPaymentState(),
                    subscriptionPurchase.getAutoRenewing(),
                    subscriptionPurchase.getExpiryTimeMillis());

            if (notificationType == Constants.GoogleSubscriptionNotificationType.SUBSCRIPTION_PURCHASED
                    || notificationType == Constants.GoogleSubscriptionNotificationType.SUBSCRIPTION_RENEWED
                    || notificationType == Constants.GoogleSubscriptionNotificationType.SUBSCRIPTION_EXPIRED) {
                log.info("[GooglePay通知] 处理通知参数，类型是：{}", notificationType);
                Long expiryTimeMillis = subscriptionPurchase.getExpiryTimeMillis();
                String orderId = subscriptionPurchase.getOrderId();
                String[] split = orderId.split("\\.\\.");
                String obfuscatedExternalAccountId = subscriptionPurchase.getObfuscatedExternalAccountId();
                if (notificationType == Constants.GoogleSubscriptionNotificationType.SUBSCRIPTION_EXPIRED) {
                    result.setType(ReceiptInfo.EXPIRE);
                } else if (notificationType == Constants.GoogleSubscriptionNotificationType.SUBSCRIPTION_RENEWED) {
                    result.setType(ReceiptInfo.RENEW);
                } else {
                    result.setType(ReceiptInfo.SUBSCRIPTION_PURCHASED);
                }
                result.setOrderNo(obfuscatedExternalAccountId);
                result.setProductId(subscriptionId);
                result.setPayType(ReceiptInfo.GOOGLE);
                result.setSandbox(subscriptionPurchase.getPurchaseType() != null);
                result.setExpiryTimeMillis(expiryTimeMillis);
                result.setTransactionId(orderId);
                result.setOriginalTransactionId(split[0]);
                log.info("[GooglePay通知] 订阅过期处理完成，orderId：{}，过期时间：{}", orderId, expiryTimeMillis);
            } else {
                log.info("[GooglePay通知] 不支持的通知类型：{}，通知参数：{}，订阅信息：{}", notificationType, param, subscriptionPurchase);
                return null;
            }
        } catch (Exception e) {
            log.error("[GooglePay通知] 处理订阅通知异常", e);
            throw AppExceptionEnum.CommonException.COMMON_ERROR;
        }
        log.info("[GooglePay通知] 处理完成，结果：{}", result);
        return result;
    }

    private ReceiptInfo processOnetimeProductNotify(JSONObject obj, String packageName) {
        log.info("[GooglePay一次性商品] 开始处理一次性商品通知，参数：{}", obj);
        JSONObject oneTimeProductNotification = obj.getJSONObject("oneTimeProductNotification");
        if (oneTimeProductNotification == null) {
            log.warn("[GooglePay一次性商品] oneTimeProductNotification为空，不属于关联业务");
            return null;
        }

        String purchaseToken = oneTimeProductNotification.getString("purchaseToken");
        if (StringUtils.isBlank(purchaseToken)) {
            log.warn("[GooglePay一次性商品] 缺少purchaseToken字段");
            return null;
        }

        Integer notificationType = oneTimeProductNotification.getInteger("notificationType");
        String sku = oneTimeProductNotification.getString("sku");
        log.info("[GooglePay一次性商品] 通知类型：{}，SKU：{}，purchaseToken：{}", notificationType, sku, purchaseToken);

        if (notificationType == Constants.GoogleOneTimeProductNotificationType.ONE_TIME_PRODUCT_CANCELED) {
            log.info("[GooglePay一次性商品] 交易已取消，跳过处理");
            return null;
        }

        AndroidPublisher publisher = getPublisherByPackage(packageName);
        if (publisher == null) {
            log.error("[GooglePay一次性商品] 获取AndroidPublisher失败，packageName：{}", packageName);
            return null;
        }

        try {
            log.info("[GooglePay一次性商品] 开始查询商品购买信息，packageName：{}，sku：{}，purchaseToken：{}", packageName, sku, purchaseToken);
            ProductPurchase productPurchase = publisher.purchases().products().get(packageName, sku, purchaseToken).execute();
            String orderId = productPurchase.getOrderId();
            log.info("[GooglePay一次性商品] 获取商品购买信息成功，orderId：{}，purchaseState：{}，purchaseType：{}，acknowledgementState：{}",
                    orderId,
                    productPurchase.getPurchaseState(),
                    productPurchase.getPurchaseType(),
                    productPurchase.getAcknowledgementState());
            String obfuscatedExternalAccountId = productPurchase.getObfuscatedExternalAccountId();
            log.info("[GooglePay一次性商品] obfuscatedExternalAccountId：{}", obfuscatedExternalAccountId);
            ReceiptInfo result = new ReceiptInfo();
            if (StringUtils. isNotBlank(obfuscatedExternalAccountId)) {
                result.setOrderNo(obfuscatedExternalAccountId);
            }
            result.setPayType(ReceiptInfo.GOOGLE);
            result.setQuantity(productPurchase.getQuantity());
            result.setPackageName(packageName);
            result.setPurchaseTime(productPurchase.getPurchaseTimeMillis());
            result.setProductId(productPurchase.getProductId());
            result.setSandbox(productPurchase.getPurchaseType() != null);
            result.setTransactionId(orderId);
            result.setType(ReceiptInfo.ONE_TIME_PRODUCT_PURCHASED);
            String[] split = orderId.split("\\.\\.");
            result.setOriginalTransactionId(split[0]);
            log.info("[GooglePay一次性商品] 处理完成，结果：{}", result);
            return result;
        } catch (IOException e) {
            log.error("[GooglePay一次性商品] 处理异常", e);
            throw AppExceptionEnum.CommonException.COMMON_ERROR;
        }
    }
}