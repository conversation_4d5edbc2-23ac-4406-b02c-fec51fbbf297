package com.kitten.pay.service.impl.processor;

import com.google.common.collect.Lists;
import com.kitten.pay.dto.ReceiptInfo;
import com.kitten.pay.dto.req.ReceiptVerifyReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class DevReceiptProcessor<T> {

    public List<ReceiptInfo> extractReceipt(ReceiptVerifyReq<T> req) {
        String productId = req.getProductId();
        Integer payType = req.getPayType();
        ReceiptInfo receiptInfo = new ReceiptInfo();
        receiptInfo.setProductId(productId);
        receiptInfo.setPayType(payType);
        receiptInfo.setSandbox(false);
        receiptInfo.setQuantity(1);
        receiptInfo.setPackageName(req.getPkg());
        long now = System.currentTimeMillis();
        receiptInfo.setPurchaseTime(now);
        receiptInfo.setTransactionId(RandomStringUtils.secure().nextNumeric(15));
        receiptInfo.setOriginalTransactionId(receiptInfo.getTransactionId());
        receiptInfo.setExpiryTimeMillis(now + 30 * 24 * 60 * 60 * 1000L);
        if (now % 2 == 0) {
            receiptInfo.setType(ReceiptInfo.ONE_TIME_PRODUCT_PURCHASED);
        } else {
            receiptInfo.setType(ReceiptInfo.SUBSCRIPTION_PURCHASED);
        }
        return Lists.newArrayList(receiptInfo);
    }
}