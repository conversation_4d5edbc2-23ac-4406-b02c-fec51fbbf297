package com.kitten.pay.service.impl;

import com.kitten.pay.common.session.SessionContext;
import com.kitten.pay.common.session.SessionUser;
import com.kitten.pay.common.session.TokenUser;
import com.kitten.pay.dto.ReceiptInfo;
import com.kitten.pay.dto.req.ApplePayReq;
import com.kitten.pay.dto.req.GooglePayReq;
import com.kitten.pay.dto.req.ReceiptVerifyReq;
import com.kitten.pay.service.PayService;
import com.kitten.pay.service.impl.processor.AppleReceiptProcessor;
import com.kitten.pay.service.impl.processor.GoogleReceiptProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PayServiceImpl implements PayService {
    @Autowired
    private GoogleReceiptProcessor googleReceiptProcessor;
    @Autowired
    private AppleReceiptProcessor appleReceiptProcessor;

    @Override
    public void googlePay(GooglePayReq req) {
        ReceiptVerifyReq<GooglePayReq> verifyReq = buildVerifyReq(req, req.getOrderNo(), req.getProductId(), ReceiptInfo.GOOGLE);
        googleReceiptProcessor.submitReceipt(verifyReq);
    }

    @Override
    public void applePay(ApplePayReq req) {
        ReceiptVerifyReq<ApplePayReq> verifyReq = buildVerifyReq(req, req.getOrderNo(), req.getProductId(), ReceiptInfo.APPLE);
        appleReceiptProcessor.submitReceipt(verifyReq);
    }

    @Override
    public void googleNotify(String s) {
        googleReceiptProcessor.onNotify(s);
    }

    @Override
    public void appleNotify(String s) {
        appleReceiptProcessor.onNotify(s);
    }

    private <T> ReceiptVerifyReq<T> buildVerifyReq(T req, String orderNo, String productId, Integer payType) {
        ReceiptVerifyReq<T> r = new ReceiptVerifyReq<>();
        SessionUser sessionUser = SessionContext.getSessionUser();
        TokenUser tokenUser = sessionUser.getTokenUser();
        Long loginId = tokenUser.getUid();
        r.setUid(loginId);
        r.setParam(req);
        r.setProductId(productId);
        r.setOrderNo(orderNo);
        r.setPayType(payType);
        r.setPkg(sessionUser.getUgar());
        return r;
    }
}
