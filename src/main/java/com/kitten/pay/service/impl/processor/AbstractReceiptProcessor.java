package com.kitten.pay.service.impl.processor;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.kitten.pay.common.constants.Constants;
import com.kitten.pay.dto.ReceiptInfo;
import com.kitten.pay.dto.req.ReceiptVerifyReq;
import com.kitten.pay.entity.PaymentsBillingv2;
import com.kitten.pay.exception.AppExceptionEnum;
import com.kitten.pay.service.PaymentsBillingv2Service;
import com.kitten.pay.third.SyncLightClient;
import com.kitten.pay.third.dto.SyncLightBillRes;
import com.kitten.pay.util.FeiShuClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Slf4j
public abstract class AbstractReceiptProcessor<T> {
    @Autowired
    private PaymentsBillingv2Service paymentsBillingv2Service;
    @Autowired
    private SyncLightClient syncLightClient;
    @Value("${spring.profiles.active}")
    private String active;
    @Autowired
    private DevReceiptProcessor<T> devReceiptProcessor;
    @Autowired
    private FeiShuClient feiShuClient;

    public void submitReceipt(ReceiptVerifyReq<T> req) {
        log.info("收据提交参数为：{}", req);
        String orderNo = req.getOrderNo();

        PaymentsBillingv2 billingv2 = paymentsBillingv2Service.selectByOrderNo(orderNo);
        if (billingv2 == null) {
            log.error("收据提交失败，预订单不存在");
            throw AppExceptionEnum.PayException.ORDER_CHECK_FAIL;
        }
        if (billingv2.getStatus() == Constants.PaymentsBillingv2Status.PAYMENT_RECEIVED) {
            log.info("收据已处理，提交收据流程结束，订单号为：{}", orderNo);
            return;
        }
        List<ReceiptInfo> receiptInfos = null;
        if (StringUtils.equals(active, "prod")) {
            receiptInfos = extractReceipt(req);
        } else {
            receiptInfos = devReceiptProcessor.extractReceipt(req);
        }
        if (CollectionUtils.isEmpty(receiptInfos)) {
            log.error("收据提交失败，收据为空");
            throw AppExceptionEnum.PayException.ORDER_CHECK_FAIL;
        }
        SyncLightBillRes syncResult = syncLightClient.syncLight(req, receiptInfos);
        if (syncResult == null || (syncResult.getCode() != 200 || syncResult.getData() == null || !Boolean.parseBoolean(syncResult.getData().toString()))) {
            log.error("客户端提交收据同步失败，响应结果为：{}", syncResult);
            feiShuClient.alarm("submit sync fail", MDC.get("traceId") + "\n" + syncResult);
            throw AppExceptionEnum.PayException.ORDER_CHECK_FAIL;
        }
        log.info("收据提交成功，订单号为：{}", orderNo);
    }

    public void onNotify(String s) {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = servletRequestAttributes.getRequest();
        log.info("收到远程服务器通知，地址为：{}", request.getRemoteHost());
        ReceiptInfo info = notify(JSONObject.parseObject(s));
        ReceiptVerifyReq<String> req = new ReceiptVerifyReq<>();
        req.setPayType(info.getPayType());
        req.setOrderNo(info.getOrderNo());
        req.setProductId(info.getProductId());
        req.setPkg(info.getPackageName());
        req.setParam(s);
        SyncLightBillRes syncResult = syncLightClient.syncLight(req, Lists.newArrayList(info));
        if (syncResult == null || (syncResult.getCode() != 200 || syncResult.getData() == null || !Boolean.parseBoolean(syncResult.getData().toString()))) {
            log.error("后台通知服务器收据同步失败，响应结果为：{}", syncResult);
            feiShuClient.alarm("notify sync fail", MDC.get("traceId") + "\n" + syncResult);
            throw AppExceptionEnum.PayException.ORDER_CHECK_FAIL;
        }
        log.info("后台通知服务器收据同步成功，同步信息为：{}", s);
    }

    protected abstract ReceiptInfo notify(JSONObject req);

    protected abstract List<ReceiptInfo> extractReceipt(ReceiptVerifyReq<T> req);
}
