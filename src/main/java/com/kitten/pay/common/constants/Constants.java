package com.kitten.pay.common.constants;

public interface Constants {
    String USER_CACHE_PREFIX = "u";
    String USER_DETAIL_CACHE_PREFIX = USER_CACHE_PREFIX + ":d:";
    int CACHE_DEFAULT_EXPIRE_HOUR = 24;

    interface Sex {
        int UNKNOWN = 0;
        int MALE = 1;
        int FEMALE = 2;
    }

    interface UserMode {
        // 审核模式
        int AUDIT_MODE = 1;
        // 非审核模式
        int NO_AUDIT_MODE = 0;
    }

    interface AccStatus {
        // 空闲
        int FREE_TIME = 0;
        // 1繁忙
        int BUSY = 1;
        // 2离线
        int OFFLINE = 2;
        // 3，未审核
        int NOT_EXAMINED = 3;
    }

    interface UserRole {
        int NORMAL = 0;
        int TEST = 1;
    }

    interface VipType {
        int NORMAL = 0;
        int VIP = 1;
        int SVIP = 2;
    }

    interface PaymentsBillingv2Status {
        /** 待付款 */
        int PAYMENT_PENDING = 0;
        /** 已付款 */
        int PAYMENT_RECEIVED = 1;
        /** 免费试用 */
        int FREE_TRIAL = 2;
        /** 其他 */
        int OTHER = 3;
        /** linked订单 */
        int LINK_ORDER = 4;
        /** 退款 */
        int REFUND = 5;
        /** 失效 */
        int STOP = 6;
        /** 一次性付费 */
        int ONE_TIME = 7;
        /** 检查订单 */
        int CHECK_ORDER = 8;
    }

    /**
     * https://developer.android.google.cn/google/play/billing/rtdn-reference#sub
     * https://developer.android.google.cn/google/play/billing/lifecycle/subscriptions?hl=zh-cn 状态之间的转换
     */
    interface GoogleSubscriptionNotificationType {
        /** 从帐号保留状态恢复了订阅。 */
        int SUBSCRIPTION_RECOVERED = 1;
        /** 续订了处于活动状态的订阅。 */
        int SUBSCRIPTION_RENEWED = 2;
        /** 自愿或非自愿地取消了订阅。如果是自愿取消，在用户取消时发送。 */
        int SUBSCRIPTION_CANCELED = 3;
        /**  购买了新的订阅。 */
        int SUBSCRIPTION_PURCHASED = 4;
        /** SUBSCRIPTION_ON_HOLD。 */
        int SUBSCRIPTION_ON_HOLD = 5;
        /** SUBSCRIPTION_IN_GRACE_PERIOD。 */
        int SUBSCRIPTION_IN_GRACE_PERIOD = 6;
        /** 用户已通过 Play > 帐号 > 订阅恢复了订阅。订阅已取消，但在用户恢复时尚未到期。如需了解详情，请参阅 [恢复]。 */
        int SUBSCRIPTION_RESTARTED = 7;
        /** 用户已成功确认订阅价格变动。 */
        int SUBSCRIPTION_PRICE_CHANGE_CONFIRMED = 8;
        /** 订阅的续订时间点已延期。 */
        int SUBSCRIPTION_DEFERRED = 9;
        /** 订阅已暂停。 */
        int SUBSCRIPTION_PAUSED = 10;
        /** 订阅暂停计划已更改。 */
        int SUBSCRIPTION_PAUSE_SCHEDULE_CHANGED = 11;
        /** 用户在到期时间之前已撤消订阅。 */
        int SUBSCRIPTION_REVOKED = 12;
        /** 订阅已到期。 */
        int SUBSCRIPTION_EXPIRED = 13;
    }

    /**
     * https://developer.android.google.cn/google/play/billing/rtdn-reference?hl=zh-cn#one-time
     */
    interface GoogleOneTimeProductNotificationType {
        /** 用户成功购买了一次性商品。 */
        int ONE_TIME_PRODUCT_PURCHASED = 1;
        /** 用户已取消待处理的一次性商品购买交易。 */
        int ONE_TIME_PRODUCT_CANCELED  = 2;
    }

    /**
     * https://developer.apple.com/documentation/appstoreservernotifications/notificationtype
     */
    interface AppleNotificationTypeV2 {
        /** 表示客户发起了应用内消费品退款请求，应用商店要求您提供消费数据 */
        String CONSUMPTION_REQUEST = "CONSUMPTION_REQUEST";
        /**
         * 通知类型及其子类型指示用户对其订阅计划进行了更改。如果子类型为UPGRADE，则用户升级了其订阅。升级将立即生效，
         * 开始一个新的计费周期，用户将收到前一周期未使用部分的按比例退款。如果子类型为DOWNGRADE，则用户将其订阅降级或交叉分级。降级将在下次更新时生效。当前活动的计划不受影响。
         * 如果子类型为空，则用户将其续订首选项更改回当前订阅，从而有效地取消降级
         */
        String DID_CHANGE_RENEWAL_PREF = "DID_CHANGE_RENEWAL_PREF";
        /**
         * 通知类型及其子类型指示用户对订阅续订状态进行了更改。如果子类型为AUTO_RENEW_ENABLED，
         * 则用户将重新启用订阅自动续订。如果子类型为AUTO_RENEW_DISABLED，则用户在请求退款后禁用订阅自动续订，或App Store禁用订阅自动更新
         */
        String DID_CHANGE_RENEWAL_STATUS = "DID_CHANGE_RENEWAL_STATUS";
        /**
         * 通知类型及其子类型指示订阅由于计费问题而无法续订。订阅进入计费重试期。如果子类型为GRACE_PERIOD，则在宽限期内继续提供服务。如果子类型为空，则订阅不在宽限期内，您可以停止提供订阅服务。
         * 通知用户其账单信息可能有问题。App Store将继续重试计费60天，或直到用户解决其计费问题或取消其订阅，以先到者为准。了解更多信息
         */
        String DID_FAIL_TO_RENEW = "DID_FAIL_TO_RENEW";
        /**
         * 通知类型及其子类型指示订阅已成功续订。如果子类型为BILLING_RECOVERY，则之前未能续订的过期订阅现在已成功续订。如果子状态为空，则活动订阅已成功自动续订新的交易周期。为客户提供订阅内容或服务的访问权限。
         */
        String DID_RENEW = "DID_RENEW";
        /**
         * 通知类型及其子类型指示订阅已过期。如果子类型为VOLUNTARY，则订阅在用户禁用订阅续订后过期。
         * 如果子类型为BILLING_RETRY，则订阅过期，因为计费重试期结束时没有成功的计费事务。如果子类型是PRICE_INCREASE，则订阅过期，因为用户不同意需要用户同意的价格上涨。
         */
        String EXPIRED = "EXPIRED";
        /**
         * 表示计费宽限期已结束，未续订订阅，因此您可以关闭对服务或内容的访问。通知用户其账单信息可能有问题。App Store将继续重试计费60天，或直到用户解决其计费问题或取消其订阅，以先到者为准
         */
        String GRACE_PERIOD_EXPIRED = "GRACE_PERIOD_EXPIRED";
        /**
         * 通知类型及其子类型指示用户兑换了促销优惠或优惠代码。
         * 如果子类型为INITIAL_BUY，
         * 则用户将该优惠兑换为首次购买。如果子类型为RESUBSCRIBE，则用户兑换了重新订阅非活动订阅的报价。如果子类型为UPGRADE，
         * 则用户兑换了一个升级其活动订阅的优惠，该优惠将立即生效。如果子类型为DOWNGRADE，则用户兑换了一个优惠，
         * 以降级其在下一个续订日期生效的活动订阅。如果用户为其活动订阅兑换了优惠，您将收到一个不带子类型的offer_redeemed通知类型。
         */
        String OFFER_REDEEMED = "OFFER_REDEEMED";
        /**
         * 通知类型及其子类型指示系统已通知用户自动续订订阅价格上涨。
         * 如果价格上涨需要用户同意，如果用户尚未对价格上涨做出响应，则子类型为PENDING，如果用户同意价格上涨，则子类别为ACCEPTED。
         * 如果价格上涨不需要用户同意，则子类型为ACCEPTED。
         * 有关系统在显示需要客户同意的订阅价格增加的价格同意表之前如何调用应用程序的详细信息，
         */
        String PRICE_INCREASE = "PRICE_INCREASE";
        /**
         * 表示App Store成功退款了应用内消费品购买、非应用内消费、自动续订订阅或非续订订阅的交易。
         * revocationDate包含退款交易的时间戳。originalTransactionId和productId标识原始事务和产品。revocationReason包含原因。
         * 要为用户请求所有退款交易的列表，请参阅App Store Server API中的Get Refund History V1。
         */
        String REFUND = "REFUND";
        /** 表示应用商店拒绝了应用开发者发起的退款请求。 */
        String REFUND_DECLINED = "REFUND_DECLINED";
        /** 表示App Store延长了开发者请求的订阅续订日期。 */
        String RENEWAL_EXTENDED = "RENEWAL_EXTENDED";
        /**
         * 表示用户有权通过家庭共享进行的应用内购买不再可通过共享进行。
         * 当购买者禁用产品的家庭共享、购买者（或家庭成员）离开家庭组或购买者要求退款并收到退款时，
         * App Store会发送此通知。您的应用程序还收到paymentQueue（_：didRevokeEntitlementsForProductIdentifiers:）调用。家庭共享适用于非消耗品应用内购买和自动续订。有关家庭共享的详细信息
         */
        String REVOKE = "REVOKE";
        /**
         * 通知类型及其子类型指示用户订阅了产品。如果子类型为INITIAL_BUY，则用户第一次通过家庭共享购买或获得订阅的访问权。
         * 如果子类型为RESUBSCRIBE，则用户通过家庭共享重新订阅或接收到对同一订阅或同一订阅组中的另一订阅的访问。
         */
        String SUBSCRIBED = "SUBSCRIBED";
        /**
         * 当您通过调用request A Test notification端点请求时，App Store服务器发送的通知类型。调用该端点以测试服务器是否正在接收通知。只有在您的请求下，您才会收到此通知
         */
        String TEST = "TEST";
    }

    interface AppleNotificationSubType {
        /** 适用于SUBSCRIBED notificationType。此子类型的通知表示用户第一次购买了订阅，或用户第一次通过家庭共享获得了对订阅的访问。 */
        String INITIAL_BUY = "INITIAL_BUY";
        /** 适用于SUBSCRIBED notificationType。此子类型的通知表示用户通过家庭共享重新订阅或接收到对同一订阅或同一订阅组中的另一订阅的访问。 */
        String RESUBSCRIBE = "RESUBSCRIBE";
        /** 此子类型的通知表示用户已降级其订阅。降级在下一次更新时生效。DID_CHANGE_renewal_PREF */
        String DOWNGRADE = "INITIAL_BUY";
        /** 适用于notificationType。此子类型的通知表示用户已升级其订阅。升级立即生效。DID_CHANGE_RENEWAL_PREF */
        String UPGRADE = "UPGRADE";
        /** 适用于notificationType。此子类型的通知表示用户启用的订阅自动更新。DID_CHANGE_renewal_STATUS */
        String AUTO_RENEW_ENABLED = "AUTO_RENEW_ENABLED";
        /** 适用于notificationType。此子类型的通知表示用户已禁用订阅自动续订，或用户请求退款后App Store已禁用订阅的自动续订。DID_CHANGE_renewal_STATUS */
        String AUTO_RENEW_DISABLED = "AUTO_RENEW_DISABLED";
        /** 适用于notificationType。具有此子类型的通知表示用户禁用订阅自动续订后订阅已过期。期满 */
        String VOLUNTARY = "VOLUNTARY";
        /** 适用于notificationType。此子类型的通知表示订阅已过期，因为订阅在计费重试期结束之前未能续订。expired */
        String BILLING_RETRY = "BILLING_RETRY";
        /** 适用于notificationType。此子类型的通知表示订阅已过期，因为用户不同意涨价 */
        String PRICE_INCREASE = "PRICE_INCREASE";
        /** 适用于notificationType。此子类型的通知表示由于计费问题，订阅无法续订；在宽限期内继续提供对订阅的访问。 */
        String GRACE_PERIOD = "GRACE_PERIOD";
        /** 适用于notificationType。此子类型的通知表示以前未能续订的过期订阅现在已成功续订。 */
        String BILLING_RECOVERY = "BILLING_RECOVERY";
        /** 适用于notificationType。此子类型的通知表示系统通知用户订阅价格上涨，但用户尚未接受。 */
        String PENDING = "PENDING";
        /** 适用于notificationType。具有此子类型的通知表示用户接受了订阅价格的增加 */
        String ACCEPTED = "ACCEPTED";
    }

    interface AppleNotificationTypeV1 {
        /**
         * 表示Apple Support取消了自动续订订阅，客户在cancellation_date_ms中的时间戳收到退款。
         */
        String CANCEL = "CANCEL";
        /**
         * 示客户发起了应用内消费品退款请求，应用商店要求您提供消费数据。
         */
        String CONSUMPTION_REQUEST = "CONSUMPTION_REQUEST";
        /**
         * 表示客户对其订阅计划进行了更改，并在下次续订时生效。当前活动的计划不受影响。
         * 检查unified_receipt中的auto_renew_product_id字段。Pending_renewal_info检索客户订阅续订的产品的产品标识符。
         */
        String DID_CHANGE_RENEWAL_PREF = "DID_CHANGE_RENEWAL_PREF";
        /**
         * 指示订阅续订状态的更改。在JSON响应中，检查auto_renew_status_change_date_ms以检索上次状态更新的日期和时间。检查auto_renew_status以获取当前续订状态。
         */
        String DID_CHANGE_RENEWAL_STATUS = "DID_CHANGE_RENEWAL_STATUS";
        /**
         * 表示由于计费问题而无法续订的订阅。选中is_in_billing_retry_period以检索订阅的当前重试状态。
         * 如果订阅处于计费宽限期，请选中grace_period_expires_date以获取新的服务到期日期。
         */
        String DID_FAIL_TO_RENEW = "DID_FAIL_TO_RENEW";
        /**
         * 表示成功自动续订过去未能续订的过期订阅。检查expires_date以确定下一个续订日期和时间
         */
        String DID_RECOVER = "DID_RECOVER";
        /**
         * 表示客户的订阅已成功自动续订新的交易周期。为客户提供订阅内容或服务的访问权限。
         */
        String DID_RENEW = "DID_RENEW";
        /**
         * 在用户首次购买订阅时发生。将latest_receipt作为令牌存储在您的服务器上，以便随时通过App Store验证用户的订阅状态。
         */
        String INITIAL_BUY = "INITIAL_BUY";
        /**
         * 指示客户通过使用应用程序界面或在帐户的“订阅”设置中的应用程序商店以交互方式续订订阅。立即提供服务。
         */
        String INTERACTIVE_RENEWAL = "INTERACTIVE_RENEWAL";
        /**
         * 表示App Store已开始要求客户同意您应用的自动续订订阅价格上涨，这需要征得同意。
         * 在unified_receipt.Pending_renewal_info对象中，price_consent_status值为0，表示用户尚未响应价格上涨。
         * 当客户同意加价时，App Store服务器将price_consent_status设置为1。
         * 通过调用App Store Server API中的Get All Subscription status端点检查最新的价格同意状态。
         * 检查JWSRenewalInfoDecodedPayload中的priceIncreaseStatus字段。您也可以调用verifyRecept查看更新的价格同意状态。
         * 有关StoreKit在显示需要客户同意的订阅价格上涨的价格同意书之前如何调用应用程序的详细信息，
         * 请参阅paymentQueueShouldShowPriceConsent（_：）。有关管理订阅价格的详细信息，请参阅管理价格。
         */
        String PRICE_INCREASE_CONSENT = "PRICE_INCREASE_CONSENT";
        /**
         * 表示App Store成功退款了应用内消费品购买、非应用内消费或非续订订阅的交易。cancellation_date_ms包含退款交易的时间戳。
         * original_transaction_id和product_id标识原始事务和产品。cancellation_reason包含原因。
         */
        String REFUND = "REFUND";
        /**
         * 表示用户有权通过家庭共享进行的应用内购买不再可通过共享进行。StoreKit在购买者禁用产品的家庭共享、
         * 购买者（或家庭成员）离开家庭组或购买者要求退款并收到退款时发送此通知。
         * 您的应用程序还将收到paymentQueue（_：didRevokeEntitlementsForProductIdentifiers:）调用。有关家庭共享的详细信息，请参阅在应用程序中支持家庭共享。
         */
        String REVOKE = "REVOKE";
        /**
         * 截至2021 3月10日，此通知不再在生产和沙箱环境中发送。更新现有代码以依赖DID_RECOVER通知类型。
         */
        String RENEWAL = "RENEWAL";
    }
}
