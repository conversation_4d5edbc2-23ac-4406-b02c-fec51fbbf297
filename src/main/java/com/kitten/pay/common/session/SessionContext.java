package com.kitten.pay.common.session;

import com.alibaba.ttl.TransmittableThreadLocal;

/**
 * <AUTHOR>
 * @date 2021/03/26
 */
public class SessionContext {

    private static final TransmittableThreadLocal<SessionUser> THREAD_LOCAL = new TransmittableThreadLocal<>();

    public static void setSessionUser(SessionUser sessionUser){
        THREAD_LOCAL.set(sessionUser);
    }

    public static SessionUser getSessionUser(){
        return THREAD_LOCAL.get();
    }

    public static void clear() {
        THREAD_LOCAL.remove();
    }
}
