package com.kitten.pay.common.session;

import com.alibaba.fastjson2.JSONObject;
import com.kitten.pay.common.constants.HeaderConstants;
import com.kitten.pay.util.FernetUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.Serializable;
import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * todo header参数命名更新
 */
@Data
public class SessionUser implements Serializable {

    private TokenUser tokenUser;
    /** 版本号，三段式，如1.0.0 */
    private String nos;
    /** 包名 */
    private String ugar;
    /** 国家码，没有传zz */
    private String pago;
    private String ip;
    /**  1：启用，0或其他: 关闭 */
    private Boolean cloak;
    /** 1：匹配， 2: 短视频，接匹配&短视频必传 */
    private String match;
    /** 用户设备固定ID */
    private String fix;

    public SessionUser(){}

    public SessionUser(HttpServletRequest req){
        String tk = req.getHeader(HeaderConstants.TK);
        this.tokenUser = buildTokenUser(tk);
        this.nos = req.getHeader(HeaderConstants.NOS);
        this.ugar = req.getHeader(HeaderConstants.UGA);
        this.ip = buildRemoteIP(req);
        this.pago = req.getHeader(HeaderConstants.PAGO);
        this.match = req.getHeader(HeaderConstants.MATCH);
        this.fix = req.getHeader(HeaderConstants.FIX);
        this.cloak = req.getHeader(HeaderConstants.CLOAK)!=null && req.getHeader(HeaderConstants.CLOAK).equals("1");
    }

    private TokenUser buildTokenUser(String tk){
        if (StringUtils.isBlank(tk)) {
            return null;
        }
        String s = FernetUtil.decryptToString(tk);
        if (StringUtils.isBlank(s)) {
            return null;
        }
        return JSONObject.parseObject(s, TokenUser.class);
    }

    /**
     * 获取访问者的ip地址
     */
    private String buildRemoteIP(HttpServletRequest req) {
        String ipAddress = req.getHeader("x-forwarded-for");
        if(ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = req.getHeader("Proxy-Client-IP");
        }
        if(ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = req.getHeader("WL-Proxy-Client-IP");
        }
        if(ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = req.getRemoteAddr();
            if(ipAddress.equals("127.0.0.1") || ipAddress.equals("0:0:0:0:0:0:0:1")){
                //根据网卡取本机配置的IP
                InetAddress inet=null;
                try {
                    inet = InetAddress.getLocalHost();
                } catch (UnknownHostException e) {
                    e.printStackTrace();
                }
                ipAddress= inet.getHostAddress();
            }
        }
        //对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
        //"***.***.***.***".length() = 15
        if(ipAddress!=null && ipAddress.length()>15){
            if(ipAddress.indexOf(",")>0){
                ipAddress = ipAddress.substring(0,ipAddress.indexOf(","));
            }
        }
        return ipAddress;
    }

    public String getClient(){
        if(this.ugar.indexOf("-")>0){
            return ugar.split("-")[0];
        }
        return "";
    }

    public String buildServiceModel(HttpServletRequest req){
        if(req.getRequestURI()!=null){
            String []path = req.getRequestURI().split("/");
            if(path.length>3){
                return path[2];
            }
        }
        return "";
    }


}
