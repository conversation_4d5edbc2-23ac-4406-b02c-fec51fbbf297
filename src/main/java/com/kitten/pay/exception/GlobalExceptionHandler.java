package com.kitten.pay.exception;

import com.kitten.pay.dto.res.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.ConversionNotSupportedException;
import org.springframework.beans.TypeMismatchException;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.http.converter.HttpMessageNotWritableException;
import org.springframework.validation.BindException;
import org.springframework.web.HttpMediaTypeNotAcceptableException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingPathVariableException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.async.AsyncRequestTimeoutException;
import org.springframework.web.multipart.MultipartException;
import org.springframework.web.multipart.support.MissingServletRequestPartException;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.servlet.http.HttpServletResponse;

@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    @ExceptionHandler(value = AppException.class)
    public R<Void> handleAppException(AppException e){
        return R.fail(e.getCode(), e.getMsg());
    }

    @ExceptionHandler(value = AdminException.class)
    public R<Void> handleAdminException(AdminException e){
        return R.fail(e.getMessage());
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(value = {MethodArgumentNotValidException.class, BindException.class})
    public R<Void> handleValidationException(Exception ex) {
        if (ex instanceof MethodArgumentNotValidException) {
            return R.fail(AppExceptionEnum.CommonException.PARAMETER_ERROR.code, ((MethodArgumentNotValidException) ex).getBindingResult().getFieldErrors().get(0).getDefaultMessage());
        }
        return R.fail(AppExceptionEnum.CommonException.PARAMETER_ERROR.code, ((BindException) ex).getBindingResult().getFieldErrors().get(0).getDefaultMessage());
    }

    @ExceptionHandler(value = Exception.class)
    public R<Void> handleException(Exception e, HttpServletResponse response){
        log.error("全局异常", e);
        HttpStatus status;
        if (e instanceof HttpRequestMethodNotSupportedException) {
            status = HttpStatus.METHOD_NOT_ALLOWED;
        } else if (e instanceof HttpMediaTypeNotSupportedException) {
            status = HttpStatus.UNSUPPORTED_MEDIA_TYPE;
        } else if (e instanceof HttpMediaTypeNotAcceptableException) {
            status = HttpStatus.NOT_ACCEPTABLE;
        } else if (e instanceof MissingPathVariableException) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
        } else if (e instanceof MissingServletRequestParameterException) {
            status = HttpStatus.BAD_REQUEST;
        } else if (e instanceof ServletRequestBindingException) {
            status = HttpStatus.BAD_REQUEST;
        } else if (e instanceof ConversionNotSupportedException) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
        } else if (e instanceof TypeMismatchException) {
            status = HttpStatus.BAD_REQUEST;
        } else if (e instanceof HttpMessageNotReadableException) {
            status = HttpStatus.BAD_REQUEST;
        } else if (e instanceof HttpMessageNotWritableException) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
        } else if (e instanceof MethodArgumentNotValidException) {
            status = HttpStatus.BAD_REQUEST;
        } else if (e instanceof MissingServletRequestPartException) {
            status = HttpStatus.BAD_REQUEST;
        } else if (e instanceof BindException) {
            status = HttpStatus.BAD_REQUEST;
        } else if (e instanceof NoHandlerFoundException) {
            status = HttpStatus.NOT_FOUND;
        } else if (e instanceof AsyncRequestTimeoutException) {
            status = HttpStatus.SERVICE_UNAVAILABLE;
        } else if (e instanceof MultipartException){
            status = HttpStatus.SERVICE_UNAVAILABLE;
        } else {
            return R.fail("server invoke error");
        }
        response.setStatus(status.value());
        return R.fail(e.getMessage());
    }
}
