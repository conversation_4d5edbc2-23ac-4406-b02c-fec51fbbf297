package com.kitten.pay.exception;

/**
 * @remark <AUTHOR>
 * @remark @date 2021/10/26
 * @remark @description:
 */
public interface AppExceptionEnum {

    /**
     * 10000 段 常规异常
     */
    interface CommonException {
        /**
         * @remark token无效
         */
        AppException INVALID_TOKEN = new AppException("INVALID_TOKEN", 1005);
        /**
         * @remark 参数错误
         */
        AppException PARAMETER_ERROR = new AppException("PARAMETER_ERROR", 1006);
        /**
         * @remark 通用操作
         */
        AppException COMMON_ERROR = new AppException("COMMON_ERROR", 1000);
        /**
         * @remark 访问过于频繁
         */
        AppException OPERATION_TOO_FAST = new AppException("OPERATION_TOO_FAST", 10020);
    }

    interface PayException {
        AppException ORDER_CHECK_FAIL = new AppException("ORDER_CHECK_FAIL", 4001);
    }
}
