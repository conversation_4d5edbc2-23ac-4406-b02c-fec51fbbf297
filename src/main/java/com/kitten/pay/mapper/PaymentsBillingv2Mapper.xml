<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kitten.pay.mapper.PaymentsBillingv2Mapper">
  <resultMap id="BaseResultMap" type="com.kitten.pay.entity.PaymentsBillingv2">
    <!--@mbg.generated-->
    <!--@Table payments_billingv2-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="transaction_id" jdbcType="VARCHAR" property="transactionId" />
    <result column="original_transaction_id" jdbcType="VARCHAR" property="originalTransactionId" />
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="product_price" jdbcType="DECIMAL" property="productPrice" />
    <result column="coins" jdbcType="INTEGER" property="coins" />
    <result column="user_uid" jdbcType="INTEGER" property="userUid" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="channel" jdbcType="SMALLINT" property="channel" />
    <result column="receipt" jdbcType="LONGVARCHAR" property="receipt" />
    <result column="extra_info" jdbcType="LONGVARCHAR" property="extraInfo" />
    <result column="first_pay" jdbcType="BOOLEAN" property="firstPay" />
    <result column="is_sandbox" jdbcType="BOOLEAN" property="isSandbox" />
    <result column="before_coins" jdbcType="INTEGER" property="beforeCoins" />
    <result column="after_coins" jdbcType="INTEGER" property="afterCoins" />
    <result column="goods_uid" jdbcType="VARCHAR" property="goodsUid" />
    <result column="pricing_uid" jdbcType="VARCHAR" property="pricingUid" />
    <result column="template_uid" jdbcType="VARCHAR" property="templateUid" />
    <result column="template_type" jdbcType="SMALLINT" property="templateType" />
    <result column="original_goods_uid" jdbcType="VARCHAR" property="originalGoodsUid" />
    <result column="original_coins" jdbcType="INTEGER" property="originalCoins" />
    <result column="discount_goods_uid" jdbcType="VARCHAR" property="discountGoodsUid" />
    <result column="discount_coins" jdbcType="INTEGER" property="discountCoins" />
    <result column="discount_active" jdbcType="BOOLEAN" property="discountActive" />
    <result column="discount_rate" jdbcType="INTEGER" property="discountRate" />
    <result column="days" jdbcType="INTEGER" property="days" />
    <result column="start_ts" jdbcType="BIGINT" property="startTs" />
    <result column="expire_ts" jdbcType="BIGINT" property="expireTs" />
    <result column="is_in_video_billing" jdbcType="BOOLEAN" property="isInVideoBilling" />
    <result column="is_anchor_link_billing" jdbcType="BOOLEAN" property="isAnchorLinkBilling" />
    <result column="anchor_uid" jdbcType="INTEGER" property="anchorUid" />
    <result column="recharge_channel" jdbcType="SMALLINT" property="rechargeChannel" />
    <result column="country" jdbcType="VARCHAR" property="country" />
    <result column="dev_os" jdbcType="SMALLINT" property="devOs" />
    <result column="is_new_user" jdbcType="BOOLEAN" property="isNewUser" />
    <result column="is_hidden" jdbcType="BOOLEAN" property="isHidden" />
    <result column="restore_hidden" jdbcType="BOOLEAN" property="restoreHidden" />
    <result column="source_income" jdbcType="DECIMAL" property="sourceIncome" />
    <result column="source_code" jdbcType="VARCHAR" property="sourceCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="source_account_id" jdbcType="BIGINT" property="sourceAccountId" />
    <result column="source_account_anchor_id" jdbcType="BIGINT" property="sourceAccountAnchorId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, order_no, transaction_id, original_transaction_id, product_id, product_price, 
    coins, user_uid, user_id, `status`, channel, receipt, extra_info, first_pay, is_sandbox, 
    before_coins, after_coins, goods_uid, pricing_uid, template_uid, template_type, original_goods_uid, 
    original_coins, discount_goods_uid, discount_coins, discount_active, discount_rate, 
    `days`, start_ts, expire_ts, is_in_video_billing, is_anchor_link_billing, anchor_uid, 
    recharge_channel, country, dev_os, is_new_user, is_hidden, restore_hidden, source_income, 
    source_code, create_time, update_time, source_account_id, source_account_anchor_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from payments_billingv2
    where id = #{id,jdbcType=BIGINT}
  </select>
</mapper>