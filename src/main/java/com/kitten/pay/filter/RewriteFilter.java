package com.kitten.pay.filter;

import com.kitten.pay.util.MaskUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingResponseWrapper;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;

/**
 * 对请求进行解密，对响应进行加密
 */
@Slf4j
public class RewriteFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, Filter<PERSON>hain filterChain)
            throws ServletException, IOException {
        // 封装请求以便读取请求体

        // 创建响应包装器以便后续加密
        ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(response);
        
        // 对URL路径进行解密
        String encryptedURI = request.getRequestURI();
        int offset = getOffset(encryptedURI);
        String decryptedURL = MaskUtil.Param.decodeUrl(request.getRequestURL().toString(), offset);
        
        // 封装解密后的请求
        RewriteRequest wrappedRequest = new RewriteRequest(request, "/app" + StringUtils.substringAfter(decryptedURL, "/app"));
        String contentType = request.getContentType(); // 从 requestWrapper 获取 contentType
        
        // 读取请求体内容
        if (isJsonContentType(contentType)) {
            // 直接从 InputStream 读取请求体
            String encryptedBody = readBody(request);
            wrappedRequest.setDecryptedBody(MaskUtil.Param.decodeJson(encryptedBody, offset));
        } else {
            // 处理URL中的查询参数
            Map<String, String[]> decryptedParams = decryptQueryParams(request.getParameterMap(), offset);
            wrappedRequest.setDecryptedParams(decryptedParams);
        }
        
        // 执行过滤链，确保请求体被读取
        filterChain.doFilter(wrappedRequest, responseWrapper); // 使用 responseWrapper
        
        // 获取原始响应内容
        byte[] originalContent = responseWrapper.getContentAsByteArray();

        if (originalContent.length > 0) {
            // 加密响应内容
            String originalResponseStr = new String(originalContent, responseWrapper.getCharacterEncoding());
            String encryptedResponseStr = encryptResponseBody(originalResponseStr, offset);

            // 写入加密后的响应
            response.setContentLength(encryptedResponseStr.getBytes().length);
            response.setContentType("application/json;charset=UTF-8"); // 根据实际情况调整
            response.getOutputStream().write(encryptedResponseStr.getBytes());
            response.getOutputStream().flush();
        } else {
            // 如果没有响应内容，复制响应包装器的内容
            responseWrapper.copyBodyToResponse();
        }
    }

    @NotNull
    private static String readBody(HttpServletRequest request) throws IOException {
        StringBuilder encryptedBodyBuilder = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(request.getInputStream(), request.getCharacterEncoding()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                encryptedBodyBuilder.append(line);
            }
        }
        return encryptedBodyBuilder.toString();
    }

    private int getOffset(String url) {
        return MaskUtil.getOffset(url);
    }

    // 解密URL
    private String decryptURI(String encryptedURI, int offset) {
        // todo 解密url
        // 例如：将 /api/v1/enc123456 解密为 /api/v1/users
        return MaskUtil.Param.decodeUrl(encryptedURI, offset); // 示例中直接返回，实际需要替换为解密逻辑
    }

    // 解密查询参数
    private Map<String, String[]> decryptQueryParams(Map<String, String[]> encryptedParams, int offset) {
        Map<String, String[]> decryptedParams = new HashMap<>();

        for (Map.Entry<String, String[]> entry : encryptedParams.entrySet()) {
            String decryptedKey = decryptParam(entry.getKey(), offset);
            decryptedParams.put(decryptedKey, entry.getValue());
        }
        return decryptedParams;
    }

    private String decryptParam(String encryptedParam, int offset) {
        return MaskUtil.decode(encryptedParam, offset);
    }

    private String encryptResponseBody(String responseBody, int offset) {
        return MaskUtil.Param.encodeJson(responseBody, offset);
    }

    /**
     * 判断请求内容类型是否为JSON
     * @param contentType 请求的Content-Type头
     * @return 如果是JSON类型返回true，否则返回false
     */
    private boolean isJsonContentType(String contentType) {
        if (contentType == null) {
            return false;
        }
        
        // 标准的JSON内容类型
        return contentType.contains("application/json") || 
               contentType.contains("application/javascript") || 
               contentType.contains("text/json") ||
               contentType.contains("text/javascript");
    }
}
