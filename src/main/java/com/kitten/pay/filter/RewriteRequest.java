package com.kitten.pay.filter;

import lombok.Setter;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

public class RewriteRequest extends HttpServletRequestWrapper {
    private final String decryptedURI;
    @Setter
    private Map<String, String[]> decryptedParams;
    @Setter
    private String decryptedBody;

    public RewriteRequest(HttpServletRequest request, String decryptedURI) {
        super(request);
        this.decryptedURI = decryptedURI;
        this.decryptedParams = new HashMap<>(request.getParameterMap());
    }

    @Override
    public String getRequestURI() {
        return decryptedURI;
    }

    @Override
    public String getParameter(String name) {
        String[] values = getParameterValues(name);
        return (values != null && values.length > 0) ? values[0] : null;
    }

    @Override
    public Map<String, String[]> getParameterMap() {
        return Collections.unmodifiableMap(decryptedParams);
    }

    @Override
    public Enumeration<String> getParameterNames() {
        return Collections.enumeration(decryptedParams.keySet());
    }

    @Override
    public String[] getParameterValues(String name) {
        return decryptedParams.get(name);
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        if (decryptedBody == null) {
            return super.getInputStream();
        }

        final ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(decryptedBody.getBytes());

        return new ServletInputStream() {
            @Override
            public boolean isFinished() {
                return false;
            }

            @Override
            public boolean isReady() {
                return true;
            }

            @Override
            public void setReadListener(ReadListener readListener) {
                // Not implemented
            }

            @Override
            public int read() {
                return byteArrayInputStream.read();
            }
        };
    }
}
