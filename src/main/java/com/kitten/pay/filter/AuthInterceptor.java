package com.kitten.pay.filter;

import com.kitten.pay.common.session.SessionContext;
import com.kitten.pay.common.session.SessionUser;
import com.kitten.pay.dto.res.R;
import com.kitten.pay.exception.AppException;
import com.kitten.pay.exception.AppExceptionEnum;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.http.HttpStatus;
import org.springframework.web.servlet.AsyncHandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.UUID;

@Slf4j
public class AuthInterceptor implements AsyncHandlerInterceptor {
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        SessionUser sessionUser = new SessionUser(request);
        if (sessionUser.getTokenUser() == null) {
            responseError(response, HttpStatus.UNAUTHORIZED, AppExceptionEnum.CommonException.INVALID_TOKEN);
            return false;
        }
        initContext(request);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        clearContext();
    }

    private void initContext(HttpServletRequest request) {
        SessionUser sessionUser = new SessionUser(request);
        if (sessionUser.getTokenUser() == null) {
            return;
        }
        SessionContext.setSessionUser(sessionUser);
        MDC.put("traceId", UUID.randomUUID().toString());
        MDC.put("uid", sessionUser.getTokenUser().getUid().toString());
        MDC.put("app", sessionUser.getUgar());
        MDC.put("version", sessionUser.getNos());
    }

    private void clearContext() {
        SessionContext.clear();
        MDC.clear();
    }

    private void responseError(HttpServletResponse response, HttpStatus status, AppException ex) {
        // 手动设置响应数据
        response.setStatus(status.value());
        response.setContentType("application/json");
        try (PrintWriter writer = response.getWriter()) {
            writer.write(R.fail(ex).toString());
        } catch (Exception e) {
            log.error("异常", e);
        }
    }
}

