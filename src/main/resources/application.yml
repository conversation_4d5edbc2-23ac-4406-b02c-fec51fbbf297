server:
  port: 20801
mybatis-plus:
  mapper-locations: classpath:com/kitten/pay/mapper/**.xml
  executor-type: REUSE
  type-aliases-package: com.kitten.pay.entity
  configuration:
    map-underscore-to-camel-case: true
spring:
  application:
    name: kitten_pay
  profiles:
    active: @spring.profiles.active@
  redis:
    lettuce:
      pool:
        max-active: 100
        max-idle: 100
        min-idle: 10
        timeBetweenEvictionRuns: 3000ms
  jackson:
    default-property-inclusion: non_null # 忽略null字段
    serialization:
      write-dates-as-timestamps: true
      write-empty-json-arrays: true
    deserialization:
      fail-on-unknown-properties: false
      fail-on-null-for-primitives: false
    time-zone: UTC