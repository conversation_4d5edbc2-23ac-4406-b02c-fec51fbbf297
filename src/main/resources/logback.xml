<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 日志存放路径 -->
	<property name="log.path" value="./logs" />
    <!-- 日志输出格式 -->
	<property name="log.pattern" value="%d{HH:mm:ss.SSS} [%thread] %-5level %logger{20} - [%method,%line] - [%X{traceId}] - [%X{app}] - [%X{version}] - [%X{uid}] - %msg%n" />

	<!-- 控制台输出 -->
	<appender name="console" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>${log.pattern}</pattern>
		</encoder>
	</appender>

	<!-- 系统日志输出 -->
	<appender name="file_info" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<!-- 循环政策：基于时间和大小创建日志文件 -->
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<!--日志文件输出的文件名-->
			<FileNamePattern>${log.path}/kitten-pay.%d{yyyy-MM-dd}.%i.log</FileNamePattern>
			<!--活动文件的大小-->
			<MaxHistory>15</MaxHistory>
			<maxFileSize>100MB</maxFileSize>
			<!--控制所有归档日志文件的总大小-->
			<totalSizeCap>1GB</totalSizeCap>
		</rollingPolicy>
		<encoder charset="UTF-8">
			<pattern>${log.pattern}</pattern>
		</encoder>

	</appender>



	<!-- 业务模块日志级别控制  -->
    <logger name="com.kitten.pay" level="info" />
	<!-- Spring日志级别控制  -->
	<logger name="org.springframework" level="warn" />
	<logger name="org.quartz" level="warn" />

	<!--系统日志-->
	<root level="info">
		<appender-ref ref="console" />
		<appender-ref ref="file_info" />
	</root>


</configuration>