
### 1.1 API概要
正式环境域名(禁止在测试环境使用):https://testbird.feishu.cn/docx/WrgLdbaN1oOonTx2jp7cOpPenLf
测试环境域名:https://api.mokichat.com

APP包名命名规则： \*\*\*.\*\*\*\*.项目名,  如com.sugar.moki(字母小写), 测试环境用上demo test dev等测试包标识
APP版本号命名规则： \*\*\*.\*\*\*.\*\*\*,  如1.0.0,第一版本1.0.0

#### 1.2.1 header数据

| 参数名   | 参数类型   | 是否必传 |         参数说明          |
|-------|:-------|:-----|:---------------------:|
| nos   | string | 是    |    版本号，三段式，如1.0.0     |
| tk    | string | 是    |        认证token        |
| pago  | string | 是    |       国家码，没有传zz       |
| ugar  | string | 是    | 设备类型包名(com.test.demo) |
| cloak | string | 是    |  cloak，1：启用，0或其他: 关闭  |
| fix   | string | 是    |       用户设备固定ID        |

#### 2.2.3 接口返回数据说明

http状态 200 业务处理成功;
自定义异常返回示例：
```json
{
    "@data": null,
    "@code": 1000,
    "@msg": "COMMON_ERROR"
}
```
|参数名|类型|说明|
|:-----  |:-----|-----|
|@data| object |  自定义返回数据 |
|@code| int |  错误码 |
|@msg| string |  错误信息 |

#### 自定义全局常见异常code,其他异常见全局异常说明文档：

| 错误码（@code） | 错误信息(@msg)         |  错误说明  |
|------------|:-------------------|:------:|
| 1000       | COMMON_ERROR       |  通用错误  |
| 1005       | INVALID_TOKEN      |  tk无效  |
| 1006       | PARAMETER_ERROR    |  参数错误  |
| 10020      | OPERATION_TOO_FAST | 访问过于频繁 |


#### 3.1 语言标识
| 国家地区      | 语言标识                      |
|-----------|:--------------------------|
| 语言   	    | ISO-639 代码                |
| 南非荷兰语 	   | af                        |
| 阿尔巴尼亚语    | sq                        |
| 阿姆哈拉语 	   | am                        |
| 阿拉伯语 	    | ar                        |
| 亚美尼亚文 	   | hy                        |
| 阿萨姆语 	    | as                        |
| 艾马拉语 	    | ay                        |
| 阿塞拜疆语 	   | az                        |
| 班巴拉语 	    | bm                        |
| 巴斯克语 	    | eu                        |
| 白俄罗斯语 	   | be                        |
| 孟加拉文 	    | bn                        |
| 博杰普尔语 	   | bho                       |
| 波斯尼亚语 	   | bs                        |
| 保加利亚语 	   | bg                        |
| 加泰罗尼亚语 	  | ca                        |
| 宿务语 	     | ceb                       |
| 中文        | （简体） 	zh-CN 或 zh (BCP-47) |
| 中文        | （ 繁体） 	zh-TW (BCP-47)     |
| 科西嘉语 	    | co                        |
| 克罗地亚语 	   | hr                        |
| 捷克语 	     | cs                        |
| 丹麦语 	     | da                        |
| 迪维希语 	    | dv                        |
| 多格来语 	    | doi                       |
| 荷兰语 	     | nl                        |
| 英语 	      | en                        |
| 世界语 	     | eo                        |
| 爱沙尼亚语 	   | et                        |
| 埃维语 	     | ee                        |
| 菲律宾语      | （塔加拉语）fil                 |
| 芬兰语 	     | fi                        |
| 法语 	      | fr                        |
| 弗里斯兰语 	   | fy                        |
| 加利西亚语 	   | gl                        |
| 格鲁吉亚语 	   | ka                        |
| 德语        | de                        |
| 希腊文 	     | el                        |
| 瓜拉尼人 	    | gn                        |
| 古吉拉特文 	   | gu                        |
| 海地克里奥尔语 	 | ht                        |
| 豪萨语 	     | ha                        |
| 夏威夷语 	    | haw                       |
| 希伯来语 	    | he    或 iw                |
| 印地语 	     | hi                        |
| 苗语 	      | hmn                       |
| 匈牙利语 	    | hu                        |
| 冰岛语 	     | is                        |
| 伊博语 	     | ig                        |
| 伊洛卡诺语 	   | ilo                       |
| 印度尼西亚语 	  | id                        |
| 爱尔兰语 	    | ga                        |
| 意大利语 	    | it                        |
| 日语 	      | ja                        |
| 爪哇语 	     | jv  或 jw                  |
| 卡纳达文 	    | kn                        |
| 哈萨克语 	    | kk                        |
| 高棉语 	     | km                        |
| 卢旺达语 	    | rw                        |
| 贡根语 	     | gom                       |
| 韩语 	      | ko                        |
| 克里奥尔语 	   | kri                       |
| 库尔德语 	    | ku                        |
| 库尔德语      | （   索拉尼）	ckb              |
| 吉尔吉斯语 	   | ky                        |
| 老挝语 	     | lo                        |
| 拉丁文 	     | la                        |
| 拉脱维亚语 	   | lv                        |
| 林格拉语 	    | ln                        |
| 立陶宛语 	    | lt                        |
| 卢干达语 	    | lg                        |
| 卢森堡语 	    | lb                        |
| 马其顿语 	    | mk                        |
| 迈蒂利语 	    | mai                       |
| 马尔加什语 	   | mg                        |
| 马来语 	     | ms                        |
| 马拉雅拉姆文 	  | ml                        |
| 马耳他语 	    | mt                        |
| 毛利语 	     | mi                        |
| 马拉地语 	    | mr                        |
| 梅泰语       | （ 曼尼普尔语） 	mni-Mtei        |
| 米佐语 	     | lus                       |
| 蒙古文 	     | mn                        |
| 缅甸语 	     | my                        |
| 尼泊尔语 	    | ne                        |
| 挪威语   	   | no                        |
| 尼杨扎语      | （齐切瓦语） 	ny                |
| 奥里亚语      | （奥里亚） 	or                 |
| 奥罗莫语 	    | om                        |
| 普什图语 	    | ps                        |
| 波斯语 	     | fa                        |
| 波兰语 	     | pl                        |
| 葡萄牙语      | （   葡萄牙、巴西） 	pt           |
| 旁遮普语 	    | pa                        |
| 克丘亚语 	    | qu                        |
| 罗马尼亚语 	   | ro                        |
| 俄语 	      | ru                        |
| 萨摩亚语 	    | sm                        |
| 梵语 	      | sa                        |
| 苏格兰盖尔语 	  | gd                        |
| 塞佩蒂语 	    | nso                       |
| 塞尔维亚语 	   | sr                        |
| 塞索托语 	    | st                        |
| 修纳语 	     | sn                        |
| 信德语 	     | sd                        |
| 僧伽罗语 	    | si                        |
| 斯洛伐克语 	   | sk                        |
| 斯洛文尼亚语 	  | sl                        |
| 索马里语 	    | so                        |
| 西班牙语 	    | es                        |
| 巽他语 	     | su                        |
| 斯瓦希里语 	   | sw                        |
| 瑞典语 	     | sv                        |
| 塔加路语      | （ 菲律宾语） 	tl               |
| 塔吉克语 	    | tg                        |
| 泰米尔语 	    | ta                        |
| 鞑靼语 	     | tt                        |
| 泰卢固语 	    | te                        |
| 泰语     	  | th                        |
| 蒂格尼亚语 	   | ti                        |
| 宗加语 	     | ts                        |
| 土耳其语 	    | tr                        |
| 土库曼语 	    | tk                        |
| 契维语       | （   阿坎语） 	ak              |
| 乌克兰语 	    | uk                        |
| 乌尔都语 	    | ur                        |
| 维吾尔语 	    | ug                        |
| 乌兹别克语 	   | uz                        |
| 越南语 	     | vi                        |
| 威尔士语 	    | cy                        |
| 班图语 	     | xh                        |
| 意第绪语 	    | yi                        |
| 约鲁巴语 	    | yo                        |
| 祖鲁语 	     | zu                        |

#### 4.1 国家图标显示方案
1.本地存储
  https://stayfoolish.feishu.cn/file/C4OIbp1W1oYimaxnTENc46NOnof
2.网络图标
地址组成：域名/region/形状/国家编码.png
形状有（circle、round、square）
示例1：https://web.mokilive.com/region/circle/AD.png
示例2：https://web.mokilive.com/region/round/AD.png
示例3：https://web.mokilive.com/region/square/AD.png

#### 5.1 自定义im消息

1.通话状态的自定义消息： "App:CallStatusMsg"
dialingId int 主叫id
calledId int 被叫id
status string 通话状态 (1、未接，2、已拒绝，3、已接听，4、取消)
startTime int64 开始时间(时间搓/毫秒)
endTime int64 结束时间(时间搓/毫秒)
自定义返回示例：
```json
{
    "dialingId": 4555112,
    "calledId": 4555113,
    "status": "1",
    "startTime": 15555555550001,
    "endTime": 15555555550001
}
```
2.名片的自定义消息： "App:CardMsg"
userId int 名片用户id
header string 名片头像
name string 名片昵称
自定义返回示例：
```json
{
    "userId": 4555112,
    "header": "img/ijljl.png",
    "name": "张三"
}
```
3.邀请用户通话的im自定义消息： "App:InviteCustom"
自定义返回示例：
```json
{
}
```
4.用户资料图片自定义消息： "App:PaidResourceCustom"
preview string 缩略图
自定义返回示例：
```json
{
    "preview": "img/pt.png"
}
```
5.用户资料视频自定义消息： "App:VideoResourceCustom"
preview string 缩略图
content string 视频
自定义返回示例：
```json
{
    "preview": "img/pt.png",
    "content": "video/pt.mp4"
}
```
6.用户忙碌转在线提醒自定义消息： "App:UserStatusOnline"
自定义返回示例：
```json
{
}
```
7.互相关注自定义消息： "App:FollowMsg"
自定义返回示例：
```json
{
     "followType": "关注类型(1_对方关注了我,2_互相关注)",
     "content": "You have already followed each other. Feel free to chat openly with your friend."
}
```
8.活动调整自定义消息： "App:ActivityMsg"
自定义返回示例：
```json
{
     "activityName": "活动类型(valentine_情人节活动)",
     "content": "You complete task, get 900 coin."
}
```
9.选妃卡片自定义消息： "App:SelectCards"
自定义返回示例：
```json
{
     "cards": [{
       "userId": "int_用户id",
       "cover": "string_用户的封面"
     }]
  
}
```
10.客服常见问题自定义消息： "App:SysCustomQuestion"
自定义返回示例：
```json
{
     "questions": [{
       "userId": "int_问题id",
       "content": "string_问题内容"
     }]
  
}
```
11.客服问题答案自定义消息： "App:SysCustomAnswer"
自定义返回示例：
```json
{
  "content": "string_问题答案"
}
```
12.优惠券自定义消息： "App:CouponCustom"
自定义返回示例：
```json
{
     "discount": "string_则扣",
     "content": "string_文案"
}
```