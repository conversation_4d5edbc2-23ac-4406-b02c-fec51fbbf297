package com.kitten.pay;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kitten.pay.anno.doc.Doc;
import com.kitten.pay.anno.doc.DocField;
import com.kitten.pay.anno.doc.DocParam;
import com.kitten.pay.util.HttpsUtils;
import com.kitten.pay.util.MaskUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PropertiesLoaderUtils;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.*;
import java.nio.charset.Charset;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
public class ApiGenerateTest {
    /** 需要生成的新接口文档的文件目录 */
    private static String SHOW_DOC_URL;
    private static Properties config;

    static {
        Resource resource = new ClassPathResource("generate.properties");
        try {
            config = PropertiesLoaderUtils.loadProperties(resource);
            SHOW_DOC_URL = config.getProperty("showDocUrl");
        } catch (IOException e) {
            log.error("读取文件失败");
        }
    }

    /** 需要替换目标字段的正则 */
    private static final Map<String, Pattern> PATTERN = new HashMap<String, Pattern>() {
        private static final long serialVersionUID = 5049226763675708995L;

        {
            put("@param", Pattern.compile(".*@param\\s+([a-zA-Z0-9]+)\\s+.*"));
            put("@return", Pattern.compile(".*@return\\s+(.*)"));
            put("@url", Pattern.compile(".*@url\\s+(.*)"));
            put("@return_param", Pattern.compile(".*@return_param\\s+([a-zA-Z0-9.]+)\\s+.*"));
            put("@json_param", Pattern.compile(".*@json_param\\s+(.*)"));
        }
    };


    private static void process(String key, String secret, int offset, String currentPackage) throws IOException {
        uploadREADME(key, secret, offset);
        List<String> apiFiles = scanModuleApiFiles();
        log.info("扫描出的文件数量为：{}",  apiFiles.size());
        List<String> all = Lists.newArrayList();
        for (String file : apiFiles) {
            List<String> convert = convert(file, offset, currentPackage);
            List<String> results = extractAvailable(convert);
            all.addAll(results);
        }
        String join = String.join("\r\n", all);
        // dataList.add(join);
        upload(join, key, secret);
    }

    private static List<String> scanModuleApiFiles() {
        String basePath = System.getProperty("user.dir");
        /** 源api文件目录模板 */
        String projectPathTemplate = "{0}/src/main/java/com/kitten/pay/controller";
        String modulePackagePath = MessageFormat.format(projectPathTemplate, basePath);
        return FileUtils.listFiles(new File(modulePackagePath), new String[]{"java", "go"}, true)
                .stream().map(File::getAbsolutePath).collect(Collectors.toList());
    }

    /**
     * 提交到showdoc服务器
     */
    private synchronized static void upload(String doc, String key, String secret) throws IOException {
        Map<String, Object> param = Maps.newHashMap();
        param.put("from", "shell");
        param.put("api_key", key);
        param.put("api_token", secret);
        param.put("content", doc);
        String s = HttpsUtils.doPost(SHOW_DOC_URL, param);
        log.info("接口文档同步到服务器结果：{}", s);
    }


    /**
     * 提交 README.md文件 到showdoc服务器
     */
    private static void uploadREADME(String key, String secret,Integer offSet) throws IOException {
        /** README.md文件目录模板 */
        String path = System.getProperty("user.dir") + "/src/test/resources/README.md";
        String doc = FileUtils.readFileToString(new File(path),"UTF-8");

        /** 替换异常返回参数名 */
        doc = doc.replaceAll("@data", MaskUtil.encode("data", offSet)).
                replaceAll("@code", MaskUtil.encode("code", offSet)).trim().
                replaceAll("@msg", MaskUtil.encode("msg", offSet));

        Map<String, Object> param = Maps.newHashMap();
        param.put("api_key", key);
        param.put("api_token", secret);
        param.put("page_title", "API文档说明");
        param.put("page_content", doc);
        param.put("cat_name", "文档说明");
        String s = HttpsUtils.doPost(SHOW_DOC_URL.split("\\?")[0] + "?s=/api/item/updateByApi", param);
        uploadAppException(key, secret, offSet);
        log.info("项目README.md文档同步到服务器结果：{}", s);
    }

    /**
     * 提交 自定义全局异常 到showdoc服务器
     */
    private static StringBuilder appExceptionBuilder =null;
    private static void buildAppException() throws IOException {
        if(appExceptionBuilder!=null){
            return;
        }
        /** AppException文件目录 */
        String path = System.getProperty("user.dir") + "/src/main/java/com/kitten/pay/exception/AppExceptionEnum.java";
        appExceptionBuilder = new StringBuilder("#### 1.0.0 接口全局异常说明\n" +
                "\n" +
                "http状态 200 业务处理成功;\n" +
                "http状态 555 自定义异常;\n" +
                "自定义异常返回示例：\n" +
                "```json\n" +
                "{\n" +
                "    \"@data\": null,\n" +
                "    \"@code\": 10001,\n" +
                "    \"@msg\": \"REGISTER_REQUIRE\"\n" +
                "}\n" +
                "```\n" +
                "|参数名|类型|说明|\n" +
                "|:-----  |:-----|-----|\n" +
                "|@data| object |  自定义返回数据 |\n" +
                "|@code| int |  错误码 |\n" +
                "|@msg| string |  错误信息 |\n" +
                "\n" +
                "#### 自定义全局常见异常code：\n" +
                "\n" +
                "| 错误码（@code）        | 错误信息(@msg)   |  错误说明  |\n" +
                "| --------   | :----- | :----:  |\n");
        /** 替换异常返回参数名 */

        List<String> appExceptionJavaSource = FileUtils.readLines(new File(path),"UTF-8");
        String msgZH = "";
        for(String line :appExceptionJavaSource){
            if(line.contains("@remark")){
                msgZH=line.substring(line.indexOf("@remark")+7);
            }
            //AppException RESTORE_FAIL = new AppException("RESTORE_FAIL", 40005);
            if(line.contains("new") && line.contains("AppException")){
                line = line.substring(line.indexOf("(")+1,line.indexOf(")")).replaceAll("\"","");
                String[] codes = line.split(",");
                String exDoc = "| " + codes[1] +" | " + codes[0] +" | " + msgZH +" |\n";
                appExceptionBuilder.append(exDoc);
                msgZH="";
            }
        }

    }

    private static void uploadAppException(String key, String secret,Integer offSet) throws IOException {
        if(appExceptionBuilder==null){
            buildAppException();
        }
        String doc = appExceptionBuilder.toString().replaceAll("@data", MaskUtil.encode("data", offSet)).
                replaceAll("@code", MaskUtil.encode("code", offSet)).trim().
                replaceAll("@msg", MaskUtil.encode("msg", offSet));

        Map<String, Object> param = Maps.newHashMap();
        param.put("api_key", key);
        param.put("api_token", secret);
        param.put("page_title", "全局异常说明");
        param.put("page_content", doc);
        param.put("cat_name", "文档说明");
        String s = HttpsUtils.doPost(SHOW_DOC_URL.split("\\?")[0] + "?s=/api/item/updateByApi", param);
        log.info("项目README.md文档同步到服务器结果：{}", s);
    }

    /**
     * 提取出需要提交到showDoc中的数据
     * @param lines 每一行的数据
     * @return 有效的注释
     */
    private static List<String> extractAvailable(List<String> lines) {
        List<String> results = Lists.newArrayList();
        List<String> temp = Lists.newArrayList();
        boolean start = false;
        for (String line : lines) {
            if (StringUtils.contains(line, "/**")) {
                start = true;
            }
            if (start) {
                temp.add(line);
            }
            if (StringUtils.contains(line, "*/")) {
                start = false;
                String doc = String.join("\r\n", temp);
                if (StringUtils.contains(doc, "@url") && StringUtils.contains(doc, "@title")) {
                    results.add(doc);
                }
                temp = Lists.newArrayList();
            }
        }
        return results;
    }


    /**
     * 进行文档注释中的key的转换
     * @param sourceFile 源文件
     * @param offset 凯撒加密偏移量
     * @return 处理后的每一行数据
     */
    private static List<String> convert(String sourceFile, int offset, String currentPackage) throws IOException {
        // 1. 先尝试通过注解方式解析
        List<String> annotationResults = convertFromAnnotation(sourceFile, offset, currentPackage);

        // 2. 使用原有的注释解析方式
        List<String> commentResults = convertFromComments(sourceFile, offset);

        // 合并结果
        List<String> results = new ArrayList<>();
        results.addAll(annotationResults);
        results.addAll(commentResults);
        return results;
    }

    private static List<String> convertFromAnnotation(String sourceFile, int offset, String currentPackage) throws IOException {
        List<String> results = new ArrayList<>();

        try {
            Class<?> clazz = Class.forName(getClassNameFromFile(sourceFile));
            for (Method method : clazz.getDeclaredMethods()) {
                Doc doc = method.getAnnotation(Doc.class);
                if (doc != null) {
                    // 检查包名过滤
                    String[] packages = doc.packages();
                    if (packages.length > 0) {
                        // 如果指定了包名且当前包不在列表中，跳过这个接口
                        if (!Arrays.asList(packages).contains(currentPackage)) {
                            continue;
                        }
                    }

                    StringBuilder docBuilder = new StringBuilder();
                    docBuilder.append("/**\n");
                    docBuilder.append(" * showdoc\n");
                    docBuilder.append(" *\n");
                    docBuilder.append(" * @title ").append(doc.title()).append("\n");

                    // 处理描述
                    if (StringUtils.isNotBlank(doc.description())) {
                        docBuilder.append(" * ").append(doc.description()).append("\n");
                    }

                    // 处理参数注解
                    Parameter[] parameters = method.getParameters();
                    for (Parameter param : parameters) {
                        processParameter(param, docBuilder, offset);
                    }
                    // 处理返回值
                    processReturnType(method, docBuilder, offset);

                    docBuilder.append(" */");
                    results.add(docBuilder.toString());
                }
            }
        } catch (Exception e) {
            log.error("Error processing annotations for file: " + sourceFile, e);
        }
        return results;
    }

    private static void processParameter(Parameter param, StringBuilder docBuilder, int offset) {
        DocParam docParam = param.getAnnotation(DocParam.class);
        Class<?> paramType = param.getType();
        Type genericType = param.getParameterizedType();

        // 检查是否是 @RequestBody 参数
        RequestBody requestBody = param.getAnnotation(RequestBody.class);
        if (requestBody != null) {
            // 生成 JSON 格式的参数示例
            Object example = generateRequestExample(genericType, new HashMap<>());
            if (example != null) {
                docBuilder.append(" * @json_param ")
                        .append(MaskUtil.Param.encodeJson(JSONObject.toJSONString(example), offset))
                        .append("\n");
            }
            return;
        }

        if (docParam != null) {
            // 处理基本类型参数
            String paramName = param.getName();
            String type = getTypeString(paramType);
            docBuilder.append(" * @param ")
                    .append(MaskUtil.encode(paramName, offset))
                    .append("   ")
                    .append(docParam.required() ? "必选" : "可选")
                    .append(" ")
                    .append(type)
                    .append(" ")
                    .append(docParam.desc());
            docBuilder.append("\n");
        } else if (!isSimpleType(paramType)) {
            // 处理复杂对象参数
            processComplexParameter(paramType, docBuilder, offset, new HashSet<>());
        }
    }

    private static String getTypeString(Class<?> type) {
        if (String.class.equals(type)) return "string";
        if (Integer.class.equals(type) || int.class.equals(type)) return "int";
        if (Long.class.equals(type) || long.class.equals(type)) return "long";
        if (Double.class.equals(type) || double.class.equals(type)) return "double";
        if (Boolean.class.equals(type) || boolean.class.equals(type)) return "bool";
        if (Date.class.isAssignableFrom(type)) return "long";
        return "string";
    }

    private static void processComplexParameter(Class<?> paramType, StringBuilder docBuilder, int offset, Set<Class<?>> processed) {
        // 防止循环引用
        if (processed.contains(paramType)) {
            return;
        }
        processed.add(paramType);

        for (Field field : getAllFields(paramType)) {
            DocParam docParam = field.getAnnotation(DocParam.class);
            if (docParam != null) {
                String fieldName = field.getName();
                String type = getTypeString(field.getType());
                docBuilder.append(" * @param ")
                        .append(MaskUtil.encode(fieldName, offset))
                        .append(" ")
                        .append(docParam.required() ? "必选" : "可选")
                        .append(" ")
                        .append(type)
                        .append(" ")
                        .append(docParam.desc());

                docBuilder.append("\n");

                // 如果字段是复杂类型，递归处理
                if (!isSimpleType(field.getType())) {
                    processComplexParameter(field.getType(), docBuilder, offset, processed);
                }
            }
        }
    }

    private static String getHttpMethod(Method method) {
        if (method.isAnnotationPresent(GetMapping.class)) {
            return "get";
        } else if (method.isAnnotationPresent(PostMapping.class)) {
            return "post";
        } else if (method.isAnnotationPresent(PutMapping.class)) {
            return "put";
        } else if (method.isAnnotationPresent(DeleteMapping.class)) {
            return "delete";
        }
        return "get"; // 默认返回get
    }

    private static String getRequestUrl(Method method) {
        Class<?> clazz = method.getDeclaringClass();
        StringBuilder url = new StringBuilder();
        // 获取类级别的路径
        RequestMapping classMapping = clazz.getAnnotation(RequestMapping.class);
        if (classMapping != null && classMapping.value().length > 0) {
            url.append(classMapping.value()[0]);
        }

        // 获取方法级别的路径
        String methodPath = "";
        if (method.isAnnotationPresent(GetMapping.class)) {
            GetMapping mapping = method.getAnnotation(GetMapping.class);
            if (mapping.value().length > 0) {
                methodPath = mapping.value()[0];
            }
        } else if (method.isAnnotationPresent(PostMapping.class)) {
            PostMapping mapping = method.getAnnotation(PostMapping.class);
            if (mapping.value().length > 0) {
                methodPath = mapping.value()[0];
            }
        } else if (method.isAnnotationPresent(PutMapping.class)) {
            PutMapping mapping = method.getAnnotation(PutMapping.class);
            if (mapping.value().length > 0) {
                methodPath = mapping.value()[0];
            }
        } else if (method.isAnnotationPresent(DeleteMapping.class)) {
            DeleteMapping mapping = method.getAnnotation(DeleteMapping.class);
            if (mapping.value().length > 0) {
                methodPath = mapping.value()[0];
            }
        } else if (method.isAnnotationPresent(RequestMapping.class)) {
            RequestMapping mapping = method.getAnnotation(RequestMapping.class);
            if (mapping.value().length > 0) {
                methodPath = mapping.value()[0];
            }
        }

        // 确保路径以/开头
        if (!methodPath.startsWith("/")) {
            url.append("/");
        }
        url.append(methodPath);

        return url.toString();
    }

    private static void processReturnType(Method method, StringBuilder docBuilder, int offset) {
        Class<?> returnType = method.getReturnType();
        if (returnType != void.class) {
            try {
                // 获取方法上的ApiDoc注解
                Doc doc = method.getAnnotation(Doc.class);
                if (doc != null) {

                    // 处理参数部分已经在processParameter中完成

                    // 生成返回值示例
                    Object example = generateResponseExample(method.getGenericReturnType(), new HashMap<>());
                    String jsonExample = JSONObject.toJSONString(example);
                    docBuilder.append(" * @return ").append(
                            MaskUtil.Param.encodeJson(jsonExample, offset)
                    ).append("\n");

                    // 按顺序添加其他文档信息
                    docBuilder.append(" * @catalog ").append(doc.catalog()).append("\n");
                    docBuilder.append(" * @title ").append(doc.title()).append("\n");
                    String description = doc.description();
                    if (StringUtils.isBlank(description)) {
                        description = doc.title();
                    }
                    docBuilder.append(" * @description ").append(description).append("\n");
                    docBuilder.append(" * @method ").append(getHttpMethod(method)).append("\n");

                    // 自动生成URL
                    String url = getRequestUrl(method);
                    docBuilder.append(" * @url ").append(MaskUtil.Param.encodeUrl("https://api.mokichat.com" + url, offset)).append("\n");

                    docBuilder.append(" * @number ").append(doc.number()).append("\n");

                    // 如果有备注，放在最后
                    if (!StringUtils.isEmpty(doc.remark())) {
                        docBuilder.append(" * @remark ").append(doc.remark()).append("\n");
                    }
                }
            } catch (Exception e) {
                log.error("Error generating return example", e);
            }
        }
    }

    private static Object generateResponseExample(Type type, Map<Type, Object> processed) {
        // 处理泛型类型
        if (type instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) type;
            Class<?> rawType = (Class<?>) parameterizedType.getRawType();
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();

            // 处理集合类型
            if (Collection.class.isAssignableFrom(rawType)) {
                List<Object> list = new ArrayList<>();
                Object example = generateResponseExample(actualTypeArguments[0], new HashMap<>(processed));
                if (example != null) {
                    list.add(example);
                }
                return list;
            } else if (Map.class.isAssignableFrom(rawType)) {
                Map<String, Object> map = new LinkedHashMap<>();
                map.put("key", generateResponseExample(actualTypeArguments[1], new HashMap<>(processed)));
                return map;
            } else {
                // 处理其他泛型类型
                return generateObjectExample(rawType, actualTypeArguments, processed);
            }
        }

        // 对于非集合类型，使用缓存机制
        if (processed.containsKey(type)) {
            Object cachedValue = processed.get(type);
            if (cachedValue instanceof JSONObject) {
                return ((JSONObject) cachedValue).clone();
            }
            return cachedValue;
        }

        Object result = null;
        if (type instanceof Class) {
            Class<?> clazz = (Class<?>) type;
            if (isSimpleType(clazz)) {
                result = getDefaultValue(clazz);
            } else if (clazz.isArray()) {
                Class<?> componentType = clazz.getComponentType();
                List<Object> list = new ArrayList<>();
                Object example = generateResponseExample(componentType, new HashMap<>(processed));
                if (example != null) {
                    list.add(example);
                }
                result = list;
            } else {
                result = generateObjectExample(clazz, null, processed);
            }
        }

        if (result != null) {
            processed.put(type, result);
        }
        return result;
    }

    private static Object generateObjectExample(Class<?> clazz, Type[] genericTypes, Map<Type, Object> processed) {
        JSONObject obj = new JSONObject();
        processed.put(clazz, obj);

        List<Field> allFields = getAllFields(clazz);
        TypeVariable<?>[] typeParameters = clazz.getTypeParameters();
        Map<String, Type> typeMap = new HashMap<>();

        // 建立泛型参数映射
        if (genericTypes != null) {
            for (int i = 0; i < typeParameters.length && i < genericTypes.length; i++) {
                typeMap.put(typeParameters[i].getName(), genericTypes[i]);
            }
        }

        for (Field field : allFields) {
            DocField docField = field.getAnnotation(DocField.class);
            if (docField != null) {
                String fieldName = field.getName();
                String desc = docField.desc();

                // 获取字段的实际类型（考虑泛型）
                Type actualType = field.getGenericType();
                if (actualType instanceof TypeVariable && typeMap.containsKey(((TypeVariable<?>) actualType).getName())) {
                    actualType = typeMap.get(((TypeVariable<?>) actualType).getName());
                }

                Object fieldValue;
                DocField.DocFormat docFormat = docField.docFormat();
                switch (docFormat) {
                    case JSON:
                        fieldValue = JSONObject.parseObject(docField.example());
                        break;
                    case JSON_ARRAY:
                        fieldValue = JSONArray.parseArray(docField.example());
                        break;
                    case RAW:
                        fieldValue = desc;
                        break;
                    case TYPE_DESC:
                        fieldValue = generateFieldDescription(field.getType(), fieldName, desc);
                        break;
                    case AUTO:
                    default:
                        String example = docField.example();
                        if (StringUtils.isNotBlank(example)) {
                            if (example.startsWith("{")) {
                                fieldValue = JSONObject.parseObject(example);
                            } else if (example.startsWith("[")) {
                                fieldValue = JSONArray.parseArray(example);
                            } else {
                                fieldValue = example;
                            }
                        } else {
                            if (isSimpleType(field.getType())) {
                                // 对于简单类型，生成带描述的字段值
                                fieldValue = generateFieldDescription(field.getType(), fieldName, desc);
                            } else {
                                // 对于复杂类型，递归处理
                                fieldValue = generateResponseExample(actualType, new HashMap<>(processed));
                                if (fieldValue == null) {
                                    fieldValue = generateFieldDescription(field.getType(), fieldName, desc);
                                }
                            }
                        }
                }

                // 只有在处理复杂类型且不是RAW格式时才在字段名后添加描述
                if (!isSimpleType(field.getType()) && StringUtils.isNotBlank(desc) && docField.docFormat() != DocField.DocFormat.RAW) {
                    fieldName = fieldName + " -> " + desc;
                }
                obj.put(fieldName, fieldValue);
            }
        }
        return obj;
    }
    private static String generateFieldDescription(Class<?> type, String fieldName, String desc) {
        StringBuilder fieldDesc = new StringBuilder(getTypeString(type))
                .append(" -> ")
                .append(fieldName);
        if (StringUtils.isNotBlank(desc)) {
            fieldDesc.append(" ").append(desc);
        }
        return fieldDesc.toString();
    }
    private static boolean isSimpleType(Class<?> clazz) {
        return clazz.isPrimitive()
                || Number.class.isAssignableFrom(clazz)
                || String.class.equals(clazz)
                || Boolean.class.equals(clazz)
                || Date.class.isAssignableFrom(clazz);
    }

    private static Object getDefaultValue(Class<?> clazz) {
        if (String.class.equals(clazz)) return "string";
        if (Integer.class.equals(clazz) || int.class.equals(clazz)) return 0;
        if (Long.class.equals(clazz) || long.class.equals(clazz)) return 0L;
        if (Double.class.equals(clazz) || double.class.equals(clazz)) return 0.0;
        if (Boolean.class.equals(clazz) || boolean.class.equals(clazz)) return false;
        if (Date.class.isAssignableFrom(clazz)) return "2024-01-01 00:00:00";
        return null;
    }

    private static String getClassNameFromFile(String sourceFile) {
        String name = StringUtils.substringAfterLast(sourceFile, "main\\java");
        return StringUtils.substringBefore(name, ".").replaceFirst("\\\\", "").replaceAll("\\\\", ".");
    }

    private static List<String> convertFromComments(String sourceFile, int offset) throws IOException {
        List<String> contents = FileUtils.readLines(new File(sourceFile), Charset.defaultCharset());
        Set<String> keySet = PATTERN.keySet();
        List<String> lines = Lists.newArrayList();
        for (String content : contents) {
            for (String key : keySet) {
                if (StringUtils.contains(content, key)) {
                    try {
                        switch (key) {
                            case "@param":
                            case "@return_param":
                                content = findAndReplace(content, key, offset);
                                break;
                            case "@return":
                            case "@json_param":
                                String jsonStr = find(content, key);
                                content = findAndReplace(content, key, MaskUtil.Param.encodeJson(jsonStr, offset));
                                break;
                            case "@url":
                                String url = StringUtils.trim(StringUtils.substringAfter(content, "@url"));
                                content = findAndReplace(content, key, MaskUtil.Param.encodeUrl(url, offset));
                                break;
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
            lines.add(content);
        }
        return lines;
    }

    private static String findAndReplace(String source, String key, int offset) {
        Pattern pattern = PATTERN.get(key);
        Matcher matcher = pattern.matcher(source);
        StringBuilder builder = new StringBuilder(source);
        while (matcher.find()){
            for (int i = 1; i <= matcher.groupCount(); i++) {
                String k = matcher.group(i);
                builder.replace(matcher.start(i), matcher.end(i), MaskUtil.encode(k, offset));
            }
        }
        return builder.toString();
    }

    private static String findAndReplace(String source, String key, String target) {
        Pattern pattern = PATTERN.get(key);
        Matcher matcher = pattern.matcher(source);
        StringBuilder builder = new StringBuilder(source);
        while (matcher.find()){
            for (int i = 1; i <= matcher.groupCount(); i++) {
                builder.replace(matcher.start(i), matcher.end(i), target);
            }
        }
        return builder.toString();
    }

    private static String find(String source, String key) {
        Pattern pattern = PATTERN.get(key);
        Matcher matcher = pattern.matcher(source);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    /**
     * 获取类的所有字段，包括父类的字段
     */
    private static List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        while (clazz != null && clazz != Object.class) {
            fields.addAll(Arrays.asList(clazz.getDeclaredFields()));
            clazz = clazz.getSuperclass();
        }
        return fields;
    }

    /**
     * 生成请求参数的示例，使用 @DocParam 注解
     */
    private static Object generateRequestExample(Type type, Map<Type, Object> processed) {
        // 对于集合类型，不使用缓存机制
        if (type instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) type;
            Class<?> rawType = (Class<?>) parameterizedType.getRawType();
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();

            if (Collection.class.isAssignableFrom(rawType)) {
                List<Object> list = new ArrayList<>();
                // 为集合类型生成一个示例元素
                Object example = generateRequestExample(actualTypeArguments[0], new HashMap<>(processed));
                if (example != null) {
                    list.add(example);
                }
                return list;
            } else if (Map.class.isAssignableFrom(rawType)) {
                Map<String, Object> map = new LinkedHashMap<>();
                map.put("key", generateRequestExample(actualTypeArguments[1], new HashMap<>(processed)));
                return map;
            }
        }

        // 对于非集合类型，使用缓存机制
        if (processed.containsKey(type)) {
            Object cachedValue = processed.get(type);
            if (cachedValue instanceof JSONObject) {
                return ((JSONObject) cachedValue).clone();
            }
            return cachedValue;
        }

        Object result = null;
        if (type instanceof Class) {
            Class<?> clazz = (Class<?>) type;
            if (isSimpleType(clazz)) {
                result = getDefaultValue(clazz);
            } else if (clazz.isArray()) {
                Class<?> componentType = clazz.getComponentType();
                List<Object> list = new ArrayList<>();
                Object example = generateRequestExample(componentType, new HashMap<>(processed));
                if (example != null) {
                    list.add(example);
                }
                result = list;
            } else {
                JSONObject obj = new JSONObject();
                processed.put(type, obj);

                List<Field> allFields = getAllFields(clazz);

                for (Field field : allFields) {
                    DocParam docParam = field.getAnnotation(DocParam.class);
                    if (docParam != null) {
                        String fieldName = field.getName();
                        String desc = docParam.desc();

                        Object fieldValue;
                        if (!isSimpleType(field.getType())) {
                            // 对于复杂类型，使用新的processed Map进行递归
                            fieldValue = generateRequestExample(field.getGenericType(), new HashMap<>(processed));
                        } else {
                            // 处理简单类型字段
                            fieldValue = getDefaultValueWithDesc(field.getType(), fieldName, desc, docParam.required());
                        }
                        obj.put(fieldName, fieldValue);
                    }
                }
                result = obj;
            }
        }

        if (result != null) {
            processed.put(type, result);
        }
        return result;
    }

    private static Object getDefaultValueWithDesc(Class<?> type, String fieldName, String desc, boolean required) {
        StringBuilder value = new StringBuilder();
        value.append(getTypeString(type))
                .append(" -> ")
                .append(fieldName);

        if (StringUtils.isNotBlank(desc)) {
            value.append(" ").append(desc);
        }

        value.append(required ? " (必填)" : " (非必填)");

        return value.toString();
    }

    public static void main(String[] args) throws IOException {
        String ele = config.getProperty("docEle");
        CompletableFuture.allOf(Arrays.stream(ele.split(StringPool.COMMA)).map(app -> CompletableFuture.runAsync(() -> {
            String c = config.getProperty(app);
            if (StringUtils.isBlank(c)) {
                log.error("【{}】配置为空，跳过", app);
                return;
            }
            String[] split = c.split(StringPool.COMMA);
            try {
                process(split[0], split[1], Integer.parseInt(split[2]), app);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            log.warn("【{}】文档生成成功", app);
        })).toArray(CompletableFuture[]::new)).join();
        log.warn("所有文档生成完毕");
    }
}
