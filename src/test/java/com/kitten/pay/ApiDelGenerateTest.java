package com.kitten.pay;

import com.alibaba.fastjson2.JSONObject;
import com.kitten.pay.util.HttpsUtils;
import com.kitten.pay.vo.ShowDocCatRes;
import com.kitten.pay.vo.ShowDocRes;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class ApiDelGenerateTest {

    public static void showDocCatInitCat(int startCatId, int endCatId, String userToken) {

        for(int catId = startCatId; catId <= endCatId; catId++){

            String getCatUrl = "https://doc.mokichat.com/mydoc/server/index.php?s=/api/catalog/catListGroup";
            Map req = new HashMap();
            req.put("item_id", catId);
            req.put("user_token", userToken);
            String json = HttpsUtils.doPost(getCatUrl, req);
            ShowDocRes res = JSONObject.parseObject(json, ShowDocRes.class);
            System.out.println("开始删除目录：" + catId);

            String delCatUrl = "https://doc.mokichat.com/mydoc/server/index.php?s=/api/catalog/delete";
            for(ShowDocCatRes r : res.getData()){
                Map delReq = new HashMap();
                delReq.put("item_id", catId);
                delReq.put("user_token", userToken);
                delReq.put("cat_id", r.getCat_id());
                HttpsUtils.doPost(delCatUrl, delReq);
            }
            System.out.println("完成删除目录：" + catId);
        }

    }

    public static void main(String[] args) {
        String userToken = "4942faf56170dab2537b13c5b1cf54277ea295a4da499ac6de754411e2272fb2";
        showDocCatInitCat(5,30, userToken);
        System.out.println("完成删除");
    }

}
