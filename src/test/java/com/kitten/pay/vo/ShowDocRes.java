package com.kitten.pay.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ShowDocRes {

    private Integer error_code;
    private List<ShowDocCatRes> data;



//"cat_id": "4894",
//                "cat_name": "对象存储",
//                "item_id": "46",
//                "s_number": "99",
//                "addtime": "2023-08-24 10:31:29",
//                "parent_cat_id": "3938",
//                "level": "3",
//                "sub": []
}
