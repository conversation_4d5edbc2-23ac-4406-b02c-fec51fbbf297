package com.kitten.pay.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ShowDocCatRes {

    private String cat_id;
    private String cat_name;
    private String item_id;
    private String s_number;
    private String addtime;
    private String parent_cat_id;
    private String level;
    private Object sub;



//"cat_id": "4894",
//                "cat_name": "对象存储",
//                "item_id": "46",
//                "s_number": "99",
//                "addtime": "2023-08-24 10:31:29",
//                "parent_cat_id": "3938",
//                "level": "3",
//                "sub": []
}
